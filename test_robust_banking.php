<?php
/**
 * Test Robust Banking System with Bank Codes from Text File
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>🏦 Robust Banking System Test</h2>";

// Test 1: Check if bank codes file exists
echo "<h3>📄 Bank Codes File Check</h3>";
$bankCodesFile = 'bank codes.txt';
if (file_exists($bankCodesFile)) {
    $fileSize = filesize($bankCodesFile);
    $lineCount = count(file($bankCodesFile));
    echo "<p style='color: green;'>✅ Bank codes file found: <code>$bankCodesFile</code></p>";
    echo "<p>📊 File size: " . number_format($fileSize) . " bytes</p>";
    echo "<p>📋 Total lines: " . number_format($lineCount) . " lines</p>";
} else {
    echo "<p style='color: red;'>❌ Bank codes file not found: <code>$bankCodesFile</code></p>";
    echo "<p>Please ensure the bank codes.txt file is in the root directory.</p>";
}

// Test 2: Parse bank codes from file
echo "<h3>🔍 Bank Codes Parsing Test</h3>";
if (file_exists($bankCodesFile)) {
    $lines = file($bankCodesFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    array_shift($lines); // Remove header
    
    $bankCodes = [];
    $parseErrors = 0;
    $sampleCodes = [];
    
    foreach ($lines as $lineNum => $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Parse the line
        $parts = preg_split('/\s+/', $line, 4);
        
        if (count($parts) >= 4) {
            $bankName = $parts[0];
            $bankCode = $parts[1];
            $branchCode = $parts[2];
            $branchName = $parts[3];
            
            // Handle multi-word bank names
            if (count($parts) > 4) {
                for ($i = 1; $i < count($parts) - 2; $i++) {
                    if (preg_match('/^\d{2}$/', $parts[$i])) {
                        $bankCode = $parts[$i];
                        $branchCode = $parts[$i + 1];
                        $bankName = implode(' ', array_slice($parts, 0, $i));
                        $branchName = implode(' ', array_slice($parts, $i + 2));
                        break;
                    }
                }
            }
            
            // Create full code
            $fullCode = $bankCode . str_pad($branchCode, 3, '0', STR_PAD_LEFT);
            
            if (preg_match('/^\d{2}$/', $bankCode) && preg_match('/^\d{1,3}$/', $branchCode)) {
                $bankCodes[$fullCode] = [
                    'bankCode' => $bankCode,
                    'branchCode' => str_pad($branchCode, 3, '0', STR_PAD_LEFT),
                    'bankName' => trim($bankName),
                    'branchName' => trim($branchName)
                ];
                
                // Collect sample codes
                if (count($sampleCodes) < 10) {
                    $sampleCodes[] = $fullCode;
                }
            } else {
                $parseErrors++;
            }
        } else {
            $parseErrors++;
        }
    }
    
    echo "<p style='color: green;'>✅ Successfully parsed " . count($bankCodes) . " bank codes</p>";
    if ($parseErrors > 0) {
        echo "<p style='color: orange;'>⚠️ Parse errors: $parseErrors lines</p>";
    }
    
    // Show sample codes
    echo "<h4>📋 Sample Bank Codes:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>Full Code</th><th>Bank</th><th>Branch</th><th>Bank Code</th><th>Branch Code</th></tr>";
    
    foreach (array_slice($sampleCodes, 0, 10) as $code) {
        $bank = $bankCodes[$code];
        echo "<tr>";
        echo "<td><strong>$code</strong></td>";
        echo "<td>" . htmlspecialchars($bank['bankName']) . "</td>";
        echo "<td>" . htmlspecialchars($bank['branchName']) . "</td>";
        echo "<td>" . $bank['bankCode'] . "</td>";
        echo "<td>" . $bank['branchCode'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} else {
    echo "<p style='color: red;'>❌ Cannot test parsing - file not found</p>";
}

// Test 3: API Endpoint Test
echo "<h3>🌐 Bank Codes API Test</h3>";
$apiUrl = 'get_bank_codes.php';
if (file_exists($apiUrl)) {
    echo "<p style='color: green;'>✅ API endpoint found: <code>$apiUrl</code></p>";
    
    // Test API response
    $apiResponse = file_get_contents('http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $apiUrl);
    if ($apiResponse) {
        $apiData = json_decode($apiResponse, true);
        if ($apiData && is_array($apiData)) {
            echo "<p style='color: green;'>✅ API returns valid JSON with " . count($apiData) . " bank codes</p>";
            
            // Show sample API response
            $sampleApiCodes = array_slice($apiData, 0, 3, true);
            echo "<h4>📋 Sample API Response:</h4>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto;'>";
            echo json_encode($sampleApiCodes, JSON_PRETTY_PRINT);
            echo "</pre>";
        } else {
            echo "<p style='color: red;'>❌ API returns invalid JSON</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to fetch API response</p>";
    }
} else {
    echo "<p style='color: red;'>❌ API endpoint not found: <code>$apiUrl</code></p>";
}

// Test 4: Database Field Compatibility
echo "<h3>🗄️ Database Compatibility Test</h3>";
try {
    $db = $database->getConnection();
    $stmt = $db->query("DESCRIBE employees");
    $columns = $stmt->fetchAll();
    
    $bankingFields = ['bank_code', 'bank_name', 'bank_branch', 'bank_account'];
    $foundFields = [];
    
    foreach ($columns as $column) {
        if (in_array($column['Field'], $bankingFields)) {
            $foundFields[] = $column['Field'];
        }
    }
    
    if (count($foundFields) === count($bankingFields)) {
        echo "<p style='color: green;'>✅ All banking fields exist in database</p>";
        
        // Test bank_code field length
        foreach ($columns as $column) {
            if ($column['Field'] === 'bank_code') {
                $type = $column['Type'];
                echo "<p>📊 bank_code field type: <code>$type</code></p>";
                
                if (strpos($type, 'varchar') !== false && (strpos($type, '(20)') !== false || strpos($type, '(10)') !== false)) {
                    echo "<p style='color: green;'>✅ bank_code field can store 5-digit codes</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ bank_code field type may need adjustment for 5-digit codes</p>";
                }
                break;
            }
        }
    } else {
        $missing = array_diff($bankingFields, $foundFields);
        echo "<p style='color: red;'>❌ Missing database fields: " . implode(', ', $missing) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 5: Form Integration Test
echo "<h3>📝 Form Integration Test</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🧪 Test Bank Code Input:</h4>";
echo "<input type='text' id='testBankCode' placeholder='Enter 5-digit bank code (e.g., 01100)' style='padding: 8px; width: 300px; margin-right: 10px;'>";
echo "<button onclick='testBankCodeValidation()' style='padding: 8px 15px;'>Test Validation</button>";
echo "<div id='testResult' style='margin-top: 10px;'></div>";
echo "</div>";

echo "<script>";
echo "function testBankCodeValidation() {";
echo "    const code = document.getElementById('testBankCode').value;";
echo "    const result = document.getElementById('testResult');";
echo "    ";
echo "    if (code.length === 5 && /^\d{5}$/.test(code)) {";
echo "        result.innerHTML = '<p style=\"color: green;\">✅ Valid format: 5-digit numeric code</p>';";
echo "    } else {";
echo "        result.innerHTML = '<p style=\"color: red;\">❌ Invalid format: Must be exactly 5 digits</p>';";
echo "    }";
echo "}";
echo "</script>";

// Summary
echo "<h3>📊 Test Summary</h3>";
$allTestsPassed = file_exists($bankCodesFile) && file_exists($apiUrl);

if ($allTestsPassed) {
    echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h4>🎉 Robust Banking System Ready!</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Bank codes file</strong> loaded with comprehensive data</li>";
    echo "<li>✅ <strong>API endpoint</strong> serving bank codes as JSON</li>";
    echo "<li>✅ <strong>5-digit codes</strong> format (bank + branch codes)</li>";
    echo "<li>✅ <strong>Auto-population</strong> of bank and branch names</li>";
    echo "<li>✅ <strong>Searchable input</strong> with validation</li>";
    echo "<li>✅ <strong>Modal reference</strong> for all bank codes</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>⚠️ Setup Required</h4>";
    echo "<p>Please ensure:</p>";
    echo "<ul>";
    if (!file_exists($bankCodesFile)) echo "<li>❌ Bank codes.txt file is in the root directory</li>";
    if (!file_exists($apiUrl)) echo "<li>❌ get_bank_codes.php API endpoint is created</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='index.php?page=employees&action=add'>Test Employee Form</a> | ";
echo "<a href='index.php?page=employees&action=list'>Back to Employee Management</a></p>";
?>
