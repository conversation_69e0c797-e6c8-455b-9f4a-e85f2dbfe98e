<?php
/**
 * Test Bank Code Parsing
 * Verify that we can correctly parse the bank codes file
 */

echo "<h2>🔍 Bank Code Parsing Test</h2>";

// Test parsing a few sample lines from the bank codes file
$testLines = [
    "Kenya Commercial Bank Limited 01 094 Head Office",
    "Kenya Commercial Bank Limited 01 340 Karen Waterfront Platinum",
    "Standard Chartered Bank Kenya Ltd 02 008 Moi Avenue",
    "Absa Bank Kenya Plc 03 001 Head Office - Vpc",
    "Co-operative Bank of Kenya Limited 11 000 Head Office"
];

echo "<h3>📋 Testing Line Parsing</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th>Original Line</th><th>Bank Name</th><th>Bank Code</th><th>Branch Code</th><th>Branch Name</th><th>Full Code</th>";
echo "</tr>";

foreach ($testLines as $line) {
    $line = trim($line);
    
    // Parse the line using the same logic as the import function
    $parts = preg_split('/\s+/', $line);
    
    $bankCode = null;
    $branchCode = null;
    $bankCodeIndex = -1;
    
    // Find the bank code (should be 2 digits) and branch code (should be 3 digits)
    for ($i = 1; $i < count($parts) - 1; $i++) {
        if (preg_match('/^\d{2}$/', $parts[$i]) && isset($parts[$i + 1]) && preg_match('/^\d{1,3}$/', $parts[$i + 1])) {
            $bankCode = $parts[$i];
            $branchCode = $parts[$i + 1];
            $bankCodeIndex = $i;
            break;
        }
    }
    
    if ($bankCode && $branchCode && $bankCodeIndex > 0) {
        // Bank name is everything before the bank code
        $bankName = implode(' ', array_slice($parts, 0, $bankCodeIndex));
        // Branch name is everything after the branch code
        $branchName = implode(' ', array_slice($parts, $bankCodeIndex + 2));
        
        // Ensure codes are properly formatted
        $bankCodeStr = str_pad(strval($bankCode), 2, '0', STR_PAD_LEFT);
        $branchCodeStr = str_pad(strval($branchCode), 3, '0', STR_PAD_LEFT);
        $fullCode = $bankCodeStr . $branchCodeStr;
        
        $status = "✅";
    } else {
        $bankName = "PARSE ERROR";
        $branchName = "PARSE ERROR";
        $fullCode = "ERROR";
        $status = "❌";
    }
    
    echo "<tr>";
    echo "<td style='font-size: 12px;'>" . htmlspecialchars($line) . "</td>";
    echo "<td><strong>" . htmlspecialchars($bankName) . "</strong></td>";
    echo "<td>$bankCode</td>";
    echo "<td>$branchCode</td>";
    echo "<td>" . htmlspecialchars($branchName) . "</td>";
    echo "<td><strong>$fullCode</strong> $status</td>";
    echo "</tr>";
}
echo "</table>";

// Now test the actual bank codes loading function
echo "<h3>🏦 Testing Full Bank Codes Loading</h3>";

function loadBankCodesData() {
    $bankCodes = [];
    $filePath = 'bank codes.txt';
    
    if (!file_exists($filePath)) {
        return [
            '01094' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Head Office'],
            '01340' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Karen Waterfront Platinum'],
            '02008' => ['bankName' => 'Standard Chartered Bank Kenya Ltd', 'branchName' => 'Moi Avenue']
        ];
    }
    
    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if ($lines === false) {
        return [];
    }
    
    // Skip header line
    array_shift($lines);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Parse the line using the improved logic
        $parts = preg_split('/\s+/', $line);
        
        if (count($parts) >= 4) {
            // Find the bank code (should be 2 digits) and branch code (should be 3 digits)
            $bankCode = null;
            $branchCode = null;
            $bankCodeIndex = -1;
            
            for ($i = 1; $i < count($parts) - 1; $i++) {
                if (preg_match('/^\d{2}$/', $parts[$i]) && isset($parts[$i + 1]) && preg_match('/^\d{1,3}$/', $parts[$i + 1])) {
                    $bankCode = $parts[$i];
                    $branchCode = $parts[$i + 1];
                    $bankCodeIndex = $i;
                    break;
                }
            }
            
            if ($bankCode && $branchCode && $bankCodeIndex > 0) {
                // Bank name is everything before the bank code
                $bankName = implode(' ', array_slice($parts, 0, $bankCodeIndex));
                // Branch name is everything after the branch code
                $branchName = implode(' ', array_slice($parts, $bankCodeIndex + 2));
                
                // Ensure codes are properly formatted
                $bankCodeStr = str_pad(strval($bankCode), 2, '0', STR_PAD_LEFT);
                $branchCodeStr = str_pad(strval($branchCode), 3, '0', STR_PAD_LEFT);
                $fullCode = $bankCodeStr . $branchCodeStr;
                
                // Validate codes
                if (preg_match('/^\d{2}$/', $bankCodeStr) && preg_match('/^\d{3}$/', $branchCodeStr)) {
                    $bankCodes[$fullCode] = [
                        'bankName' => trim($bankName),
                        'branchName' => trim($branchName)
                    ];
                }
            }
        }
    }
    
    return $bankCodes;
}

$bankCodesData = loadBankCodesData();
echo "<p style='color: green;'><strong>✅ Loaded " . count($bankCodesData) . " bank codes from file</strong></p>";

// Test specific codes that should work
$testCodes = ['01094', '01340', '02008', '03001', '11000'];
echo "<h3>🎯 Testing Specific Bank Codes</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Code</th><th>Bank Name</th><th>Branch Name</th><th>Status</th></tr>";

foreach ($testCodes as $code) {
    if (isset($bankCodesData[$code])) {
        echo "<tr>";
        echo "<td><strong>$code</strong></td>";
        echo "<td>{$bankCodesData[$code]['bankName']}</td>";
        echo "<td>{$bankCodesData[$code]['branchName']}</td>";
        echo "<td style='color: green;'>✅ Found</td>";
        echo "</tr>";
    } else {
        echo "<tr>";
        echo "<td><strong>$code</strong></td>";
        echo "<td colspan='2'>Not found</td>";
        echo "<td style='color: red;'>❌ Missing</td>";
        echo "</tr>";
    }
}
echo "</table>";

echo "<h3>📊 Summary</h3>";
if (isset($bankCodesData['01094'])) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ Bank Code Parsing is Working!</h4>";
    echo "<p>The bank codes file is being parsed correctly. The issue with the import must be elsewhere.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li>Check the debug logs during import</li>";
    echo "<li>Verify the database columns exist</li>";
    echo "<li>Test with a simple CSV import</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Bank Code Parsing Issue</h4>";
    echo "<p>The bank codes are not being parsed correctly. This is likely the root cause.</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='index.php?page=employees&action=bulk_import' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📤 Test Import Now</a>";
echo "</div>";
?>
