<?php
/**
 * Test Number Parsing Function
 */

// Include the parsing function
function parseFormattedNumber($value) {
    if (empty($value)) return 0;
    
    // Convert to string and trim
    $value = trim((string)$value);
    
    // Remove commas, spaces, and common currency symbols
    $value = preg_replace('/[,\s]/', '', $value);
    $value = preg_replace('/[^\d\.\-]/', '', $value);
    
    // Convert to float
    return floatval($value);
}

echo "<h2>🧮 Number Parsing Test</h2>";

// Test cases
$testCases = [
    '7,000' => 7000,
    '7000.00' => 7000,
    '7000' => 7000,
    '7,000.50' => 7000.50,
    '50,000' => 50000,
    '1,234,567.89' => 1234567.89,
    'KES 7,000' => 7000,
    '$7,000' => 7000,
    '7 000' => 7000,
    '7.000,50' => 7000.50, // European format
    '' => 0,
    '0' => 0,
    'invalid' => 0
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Input</th><th>Expected</th><th>Actual</th><th>Status</th></tr>";

$passed = 0;
$total = count($testCases);

foreach ($testCases as $input => $expected) {
    $actual = parseFormattedNumber($input);
    $status = ($actual == $expected) ? '✅ PASS' : '❌ FAIL';
    
    if ($actual == $expected) {
        $passed++;
    }
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($input) . "</td>";
    echo "<td>$expected</td>";
    echo "<td>$actual</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h3>Results: $passed/$total tests passed</h3>";

if ($passed == $total) {
    echo "<p style='color: green; font-weight: bold;'>🎉 All tests passed! Number parsing is working correctly.</p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ Some tests failed. Check the implementation.</p>";
}

echo "<hr>";
echo "<p><a href='index.php?page=employees&action=list'>Back to Employee Management</a></p>";
?>
