<?php
/**
 * Test String Field Preservation (Leading Zeros)
 */

echo "<h2>🔢 String Field Preservation Test</h2>";

// Test cases for string fields that should preserve leading zeros
$testCases = [
    'bank_code' => [
        'input' => ['01', '02', '11', '001', '1', '10'],
        'expected' => ['01', '02', '11', '001', '1', '10']
    ],
    'nssf_number' => [
        'input' => ['000123', '001234', '123456', '0001', '1'],
        'expected' => ['000123', '001234', '123456', '0001', '1']
    ],
    'nhif_number' => [
        'input' => ['000456', '001789', '789012', '0002', '2'],
        'expected' => ['000456', '001789', '789012', '0002', '2']
    ],
    'kra_pin' => [
        'input' => ['A0********B', 'A********7C', 'A********9D'],
        'expected' => ['A0********B', 'A********7C', 'A********9D']
    ],
    'id_number' => [
        'input' => ['********', '********', '********', '********'],
        'expected' => ['********', '********', '********', '********']
    ],
    'account_number' => [
        'input' => ['0********7', '********78', '********90', '**********'],
        'expected' => ['0********7', '********78', '********90', '**********']
    ]
];

echo "<h3>📋 Test Results:</h3>";

$allPassed = true;

foreach ($testCases as $fieldName => $testData) {
    echo "<h4>🔍 Testing: $fieldName</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
    echo "<tr><th>Input</th><th>Expected</th><th>Processed</th><th>Status</th></tr>";
    
    foreach ($testData['input'] as $index => $input) {
        $expected = $testData['expected'][$index];
        
        // Simulate the processing logic from bulk import
        $processed = !empty($input) ? trim($input) : null;
        
        $status = ($processed === $expected) ? '✅ PASS' : '❌ FAIL';
        $rowClass = ($processed === $expected) ? '' : 'style="background-color: #ffebee;"';
        
        if ($processed !== $expected) {
            $allPassed = false;
        }
        
        echo "<tr $rowClass>";
        echo "<td>" . htmlspecialchars($input) . "</td>";
        echo "<td>" . htmlspecialchars($expected) . "</td>";
        echo "<td>" . htmlspecialchars($processed ?? 'NULL') . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

echo "<h3>🎯 Overall Result:</h3>";

if ($allPassed) {
    echo "<p style='color: green; font-weight: bold; font-size: 18px;'>🎉 All tests passed! String fields preserve leading zeros correctly.</p>";
} else {
    echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ Some tests failed. Check the implementation.</p>";
}

echo "<hr>";

echo "<h3>📝 Sample CSV Data for Testing:</h3>";
echo "<div style='background-color: #f5f5f5; padding: 15px; border-radius: 5px; font-family: monospace;'>";
echo "first_name,last_name,basic_salary,bank_code,nssf_number,nhif_number,kra_pin,id_number,account_number<br>";
echo "John,Doe,50000,01,000123,000456,A0********B,********,0********7<br>";
echo "Jane,Smith,75000,02,001234,001789,A********7C,********,********78<br>";
echo "Bob,Johnson,60000,11,123456,789012,A********9D,********,********90<br>";
echo "</div>";

echo "<p><strong>💡 Key Points:</strong></p>";
echo "<ul>";
echo "<li>Bank codes like <code>01</code>, <code>02</code> should remain as strings</li>";
echo "<li>NSSF numbers like <code>000123</code> should preserve leading zeros</li>";
echo "<li>NHIF numbers like <code>000456</code> should preserve leading zeros</li>";
echo "<li>ID numbers like <code>********</code> should preserve leading zeros</li>";
echo "<li>Account numbers like <code>0********7</code> should preserve leading zeros</li>";
echo "<li>KRA PINs should preserve their exact format</li>";
echo "</ul>";

echo "<hr>";
echo "<p><a href='index.php?page=employees&action=list'>Back to Employee Management</a></p>";
?>
