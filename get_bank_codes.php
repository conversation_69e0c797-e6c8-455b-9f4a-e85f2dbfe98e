<?php
/**
 * Get Bank Codes API
 * 
 * Reads bank codes from bank codes.txt file and returns them as JSON
 * for use in the employee form bank code validation and auto-completion
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

try {
    $bankCodes = [];
    $filePath = 'bank codes.txt';
    
    if (!file_exists($filePath)) {
        throw new Exception('Bank codes file not found');
    }
    
    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    if ($lines === false) {
        throw new Exception('Failed to read bank codes file');
    }
    
    // Skip header line
    array_shift($lines);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Parse the line: Bank Name Bank Code Branch Code Branch Name
        $parts = preg_split('/\s+/', $line, 4);
        
        if (count($parts) >= 4) {
            $bankName = $parts[0];
            $bankCode = $parts[1];
            $branchCode = $parts[2];
            $branchName = $parts[3];
            
            // Handle multi-word bank names
            if (count($parts) > 4) {
                // Find where the bank code starts (should be 2 digits)
                $foundBankCode = false;
                for ($i = 1; $i < count($parts) - 2; $i++) {
                    if (preg_match('/^\d{2}$/', $parts[$i])) {
                        $bankCode = $parts[$i];
                        $branchCode = $parts[$i + 1];
                        $bankName = implode(' ', array_slice($parts, 0, $i));
                        $branchName = implode(' ', array_slice($parts, $i + 2));
                        $foundBankCode = true;
                        break;
                    }
                }
                
                if (!$foundBankCode) {
                    continue; // Skip malformed lines
                }
            }
            
            // Ensure bank code has leading zero if needed (convert to string)
            $bankCodeStr = str_pad(strval($bankCode), 2, '0', STR_PAD_LEFT);
            $branchCodeStr = str_pad(strval($branchCode), 3, '0', STR_PAD_LEFT);

            // Create full code (bank code + branch code) as string
            $fullCode = $bankCodeStr . $branchCodeStr;

            // Validate codes (ensure they're numeric but keep as strings)
            if (preg_match('/^\d{2}$/', $bankCodeStr) && preg_match('/^\d{3}$/', $branchCodeStr)) {
                $bankCodes[$fullCode] = [
                    'bankCode' => $bankCodeStr,
                    'branchCode' => $branchCodeStr,
                    'bankName' => trim($bankName),
                    'branchName' => trim($branchName),
                    'fullCode' => $fullCode
                ];
            }
        }
    }
    
    // Sort by full code
    ksort($bankCodes);
    
    echo json_encode($bankCodes, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // Return fallback bank codes if file reading fails
    $fallbackCodes = [
        '01094' => [
            'bankCode' => '01',
            'branchCode' => '094',
            'bankName' => 'Kenya Commercial Bank Limited',
            'branchName' => 'Head Office',
            'fullCode' => '01094'
        ],
        '01100' => [
            'bankCode' => '01',
            'branchCode' => '100',
            'bankName' => 'Kenya Commercial Bank Limited',
            'branchName' => 'Moi Avenue Nairobi',
            'fullCode' => '01100'
        ],
        '01105' => [
            'bankCode' => '01',
            'branchCode' => '105',
            'bankName' => 'Kenya Commercial Bank Limited',
            'branchName' => 'Kisumu',
            'fullCode' => '01105'
        ],
        '02008' => [
            'bankCode' => '02',
            'branchCode' => '008',
            'bankName' => 'Standard Chartered Bank Kenya Ltd',
            'branchName' => 'Moi Avenue',
            'fullCode' => '02008'
        ],
        '02015' => [
            'bankCode' => '02',
            'branchCode' => '015',
            'bankName' => 'Standard Chartered Bank Kenya Ltd',
            'branchName' => 'Westlands',
            'fullCode' => '02015'
        ],
        '03001' => [
            'bankCode' => '03',
            'branchCode' => '001',
            'bankName' => 'Absa Bank Kenya Plc',
            'branchName' => 'Head Office - Vpc',
            'fullCode' => '03001'
        ],
        '07101' => [
            'bankCode' => '07',
            'branchCode' => '101',
            'bankName' => 'NCBA Bank Kenya Plc',
            'branchName' => 'City Centre',
            'fullCode' => '07101'
        ],
        '11000' => [
            'bankCode' => '11',
            'branchCode' => '000',
            'bankName' => 'Co-operative Bank of Kenya Limited',
            'branchName' => 'Head Office',
            'fullCode' => '11000'
        ],
        '12000' => [
            'bankCode' => '12',
            'branchCode' => '000',
            'bankName' => 'National Bank Of Kenya',
            'branchName' => 'Central Business Unit',
            'fullCode' => '12000'
        ],
        '17001' => [
            'bankCode' => '17',
            'branchCode' => '001',
            'bankName' => 'Equity Bank Kenya Limited',
            'branchName' => 'Head Office',
            'fullCode' => '17001'
        ]
    ];
    
    echo json_encode($fallbackCodes, JSON_PRETTY_PRINT);
}
?>
