<?php
/**
 * Empty Employees Table for Clean Import
 * 
 * This script safely empties the employees table while preserving the table structure
 * and provides a backup option before deletion.
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in and has admin privileges
if (!isset($_SESSION['user_id'])) {
    die('<div style="color: red; font-weight: bold;">❌ Access denied. Please log in first.</div>');
}

// For safety, require admin role for this operation
if (!isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    die('<div style="color: red; font-weight: bold;">❌ Access denied. Admin privileges required for this operation.</div>');
}

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('<div style="color: red; font-weight: bold;">❌ Database connection failed.</div>');
}

echo "<h2>🗑️ Empty Employees Table</h2>";

// Check if action is confirmed
$action = $_GET['action'] ?? '';
$confirm = $_GET['confirm'] ?? '';

if ($action === 'empty' && $confirm === 'yes') {
    try {
        // First, get count of existing employees
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM employees WHERE company_id = ?");
        $stmt->execute([$_SESSION['company_id']]);
        $result = $stmt->fetch();
        $employeeCount = $result['count'];
        
        if ($employeeCount == 0) {
            echo "<div style='color: orange; padding: 15px; border: 1px solid #ffc107; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>ℹ️ No Employees Found</h4>";
            echo "<p>The employees table is already empty for your company.</p>";
            echo "</div>";
        } else {
            // Create a backup first (optional - save to a backup table)
            echo "<h3>📋 Creating Backup</h3>";
            
            $backupTableName = 'employees_backup_' . date('Y_m_d_H_i_s');
            $createBackupSQL = "CREATE TABLE $backupTableName AS SELECT * FROM employees WHERE company_id = ?";
            
            try {
                $stmt = $db->prepare($createBackupSQL);
                $stmt->execute([$_SESSION['company_id']]);
                echo "<p style='color: green;'>✅ Backup created: $backupTableName ($employeeCount records)</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Backup creation failed (continuing anyway): " . $e->getMessage() . "</p>";
            }
            
            // Delete employees for current company only
            echo "<h3>🗑️ Emptying Employees Table</h3>";
            
            $stmt = $db->prepare("DELETE FROM employees WHERE company_id = ?");
            $stmt->execute([$_SESSION['company_id']]);
            
            $deletedCount = $stmt->rowCount();
            
            echo "<div style='color: green; padding: 15px; border: 1px solid #28a745; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>🎉 Success!</h4>";
            echo "<p><strong>$deletedCount employees</strong> have been removed from your company.</p>";
            echo "<p>The employees table is now empty and ready for a clean import.</p>";
            if (isset($backupTableName)) {
                echo "<p><small>Backup saved as: <code>$backupTableName</code></small></p>";
            }
            echo "</div>";
            
            // Log the activity
            logActivity('employees_table_emptied', "Emptied employees table - $deletedCount records removed");
        }
        
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='index.php?page=employees&action=list' style='background: #006b3f; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Employee Management</a>";
        echo "<a href='download_template.php?type=employees&format=csv' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Download CSV Template</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 15px; border: 1px solid #dc3545; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>❌ Error</h4>";
        echo "<p>Failed to empty employees table: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
} else {
    // Show confirmation form
    try {
        // Get current employee count
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM employees WHERE company_id = ?");
        $stmt->execute([$_SESSION['company_id']]);
        $result = $stmt->fetch();
        $employeeCount = $result['count'];
        
        // Get sample employees for display
        $stmt = $db->prepare("SELECT first_name, last_name, employee_number FROM employees WHERE company_id = ? ORDER BY created_at DESC LIMIT 5");
        $stmt->execute([$_SESSION['company_id']]);
        $sampleEmployees = $stmt->fetchAll();
        
        echo "<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ Confirmation Required</h3>";
        
        if ($employeeCount == 0) {
            echo "<p>The employees table is already empty for your company. No action needed.</p>";
            echo "<a href='index.php?page=employees&action=list' style='background: #006b3f; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Employee Management</a>";
        } else {
            echo "<p>You are about to <strong>permanently delete all $employeeCount employees</strong> from your company.</p>";
            
            if (!empty($sampleEmployees)) {
                echo "<h4>📋 Current Employees (Sample):</h4>";
                echo "<ul>";
                foreach ($sampleEmployees as $emp) {
                    echo "<li>" . htmlspecialchars($emp['first_name'] . ' ' . $emp['last_name']) . " (" . htmlspecialchars($emp['employee_number']) . ")</li>";
                }
                if ($employeeCount > 5) {
                    echo "<li><em>... and " . ($employeeCount - 5) . " more employees</em></li>";
                }
                echo "</ul>";
            }
            
            echo "<h4>🔒 Safety Measures:</h4>";
            echo "<ul>";
            echo "<li>✅ A backup will be created before deletion</li>";
            echo "<li>✅ Only employees from your company will be affected</li>";
            echo "<li>✅ Table structure will be preserved</li>";
            echo "<li>✅ This action will be logged for audit purposes</li>";
            echo "</ul>";
            
            echo "<h4>⚡ This action cannot be undone!</h4>";
            echo "<p>Are you sure you want to proceed?</p>";
            
            echo "<div style='margin-top: 20px;'>";
            echo "<a href='?action=empty&confirm=yes' onclick='return confirm(\"Are you absolutely sure you want to delete all $employeeCount employees? This cannot be undone!\")' style='background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-right: 10px; font-weight: bold;'>🗑️ Yes, Empty Table</a>";
            echo "<a href='index.php?page=employees&action=list' style='background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>Cancel</a>";
            echo "</div>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 15px; border: 1px solid #dc3545; border-radius: 5px;'>";
        echo "<h4>❌ Error</h4>";
        echo "<p>Failed to check employees table: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

echo "<hr>";
echo "<h3>📝 Next Steps After Emptying:</h3>";
echo "<ol>";
echo "<li><strong>Download Template:</strong> Get the latest CSV template with updated bank codes</li>";
echo "<li><strong>Prepare Data:</strong> Fill in your employee information with proper formatting</li>";
echo "<li><strong>Import Data:</strong> Use the bulk import feature to upload your CSV</li>";
echo "<li><strong>Verify Results:</strong> Check that all employees were imported correctly</li>";
echo "</ol>";

echo "<div style='background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>💡 Pro Tips for Clean Import:</h4>";
echo "<ul>";
echo "<li>Use the updated CSV template with latest bank codes (01, 02, 03, etc.)</li>";
echo "<li>Format salaries with commas: 50,000 or 50000.00</li>";
echo "<li>Preserve leading zeros in bank codes: 01, 02 (not 1, 2)</li>";
echo "<li>Use proper phone format: +254XXXXXXXXX or 0XXXXXXXXX</li>";
echo "<li>Include bank branches for automatic population</li>";
echo "</ul>";
echo "</div>";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Empty Employees Table</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 50px auto;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        
        h2 {
            color: #006b3f;
            border-bottom: 3px solid #ce1126;
            padding-bottom: 10px;
        }
        
        h3 {
            color: #333;
            margin-top: 30px;
        }
        
        a {
            display: inline-block;
            margin: 5px;
        }
        
        code {
            background: #f1f1f1;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
</body>
</html>
