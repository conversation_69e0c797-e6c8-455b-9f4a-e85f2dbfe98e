<?php
/**
 * AJAX Endpoint: Update Employment Type
 * 
 * Updates the contract_type field for an employee in real-time
 * when the employment type dropdown changes in the frontend.
 */

session_start();
require_once '../config/database.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['company_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized: Please log in'
    ]);
    exit;
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate required parameters
if (!isset($input['employee_id']) || !isset($input['employment_type'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Missing required parameters: employee_id and employment_type'
    ]);
    exit;
}

$employeeId = intval($input['employee_id']);
$employmentType = trim($input['employment_type']);
$companyId = $_SESSION['company_id'];

// Validate employee ID
if ($employeeId <= 0) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid employee ID'
    ]);
    exit;
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('Database connection failed');
    }
    
    // Verify employee belongs to the current company
    $stmt = $db->prepare("SELECT id, first_name, last_name, contract_type FROM employees WHERE id = ? AND company_id = ?");
    $stmt->execute([$employeeId, $companyId]);
    $employee = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$employee) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Employee not found or access denied'
        ]);
        exit;
    }
    
    // Get valid employment types for this company
    $stmt = $db->prepare("SELECT type_name FROM employment_types WHERE company_id = ? AND is_active = 1");
    $stmt->execute([$companyId]);
    $validTypes = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // If no types in database, use default fallback
    if (empty($validTypes)) {
        $validTypes = ['contract', 'permanent', 'casual', 'intern', 'consultant', 'probation'];
    }
    
    // Validate employment type
    if (!empty($employmentType) && !in_array($employmentType, $validTypes)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Invalid employment type: ' . $employmentType,
            'valid_types' => $validTypes
        ]);
        exit;
    }
    
    // Update the employment type
    $stmt = $db->prepare("UPDATE employees SET contract_type = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND company_id = ?");
    $result = $stmt->execute([$employmentType ?: null, $employeeId, $companyId]);
    
    if ($result) {
        // Log the change for audit trail
        try {
            $stmt = $db->prepare("INSERT INTO activity_logs (user_id, action, description, ip_address) VALUES (?, ?, ?, ?)");
            $stmt->execute([
                $_SESSION['user_id'] ?? null,
                'employment_type_update',
                "Updated employment type for {$employee['first_name']} {$employee['last_name']} from '{$employee['contract_type']}' to '$employmentType'",
                $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
        } catch (Exception $e) {
            // Log error but don't fail the main operation
            error_log("Failed to log employment type update: " . $e->getMessage());
        }
        
        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'Employment type updated successfully',
            'data' => [
                'employee_id' => $employeeId,
                'employee_name' => $employee['first_name'] . ' ' . $employee['last_name'],
                'old_type' => $employee['contract_type'],
                'new_type' => $employmentType,
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ]);
    } else {
        throw new Exception('Failed to update employment type');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
    
    // Log the error
    error_log("Employment type update error: " . $e->getMessage());
}
?>
