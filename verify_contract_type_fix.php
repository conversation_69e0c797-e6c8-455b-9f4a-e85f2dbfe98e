<?php
/**
 * Verify Contract Type Fix
 * Quick verification that the contract_type default has been updated
 */

session_start();
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('❌ Database connection failed.');
}

echo "<h2>✅ Contract Type Default Verification</h2>";

try {
    // Check the current column definition
    $stmt = $db->query("DESCRIBE employees");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $contractTypeColumn = null;
    foreach ($columns as $column) {
        if ($column['Field'] === 'contract_type') {
            $contractTypeColumn = $column;
            break;
        }
    }
    
    if ($contractTypeColumn) {
        echo "<div style='background: " . ($contractTypeColumn['Default'] === 'contract' ? "#d4edda" : "#f8d7da") . "; border: 1px solid " . ($contractTypeColumn['Default'] === 'contract' ? "#c3e6cb" : "#f5c6cb") . "; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        
        if ($contractTypeColumn['Default'] === 'contract') {
            echo "<h4>🎉 Success! Contract Type Default Fixed</h4>";
            echo "<p><strong>✅ Database Default:</strong> contract_type now defaults to 'contract'</p>";
        } else {
            echo "<h4>❌ Issue: Contract Type Default Not Fixed</h4>";
            echo "<p><strong>❌ Database Default:</strong> contract_type still defaults to '{$contractTypeColumn['Default']}'</p>";
            echo "<p><strong>Action needed:</strong> Run the fix script: <a href='fix_contract_type_default.php'>fix_contract_type_default.php</a></p>";
        }
        
        echo "<p><strong>Current Column Definition:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Type:</strong> {$contractTypeColumn['Type']}</li>";
        echo "<li><strong>Default:</strong> {$contractTypeColumn['Default']}</li>";
        echo "<li><strong>Null:</strong> {$contractTypeColumn['Null']}</li>";
        echo "</ul>";
        echo "</div>";
        
        // Test what happens with new employee creation
        if ($contractTypeColumn['Default'] === 'contract') {
            echo "<h3>🧪 Testing New Employee Default</h3>";
            
            // Check if we have a company to test with
            $stmt = $db->query("SELECT id FROM companies LIMIT 1");
            $companyId = $stmt->fetchColumn();
            
            if ($companyId) {
                try {
                    $db->beginTransaction();
                    
                    // Insert test employee without contract_type
                    $testEmployeeNumber = 'VERIFY_' . time();
                    $stmt = $db->prepare("
                        INSERT INTO employees (company_id, employee_number, first_name, last_name, email, hire_date) 
                        VALUES (?, ?, 'Test', 'Employee', '<EMAIL>', CURDATE())
                    ");
                    $stmt->execute([$companyId, $testEmployeeNumber]);
                    
                    // Check what default was used
                    $stmt = $db->prepare("SELECT contract_type FROM employees WHERE employee_number = ?");
                    $stmt->execute([$testEmployeeNumber]);
                    $actualDefault = $stmt->fetchColumn();
                    
                    $db->rollback(); // Clean up test data
                    
                    echo "<div style='background: " . ($actualDefault === 'contract' ? "#d4edda" : "#fff3cd") . "; border: 1px solid " . ($actualDefault === 'contract' ? "#c3e6cb" : "#ffeaa7") . "; padding: 15px; border-radius: 5px;'>";
                    echo "<p><strong>Test Result:</strong> New employee got contract_type = '<strong>$actualDefault</strong>'</p>";
                    
                    if ($actualDefault === 'contract') {
                        echo "<p style='color: green;'>✅ Perfect! New employees will default to 'contract'</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ Unexpected: New employees are getting '$actualDefault' instead of 'contract'</p>";
                    }
                    echo "</div>";
                    
                } catch (Exception $e) {
                    $db->rollback();
                    echo "<p style='color: red;'>❌ Test failed: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ No companies found - cannot test new employee creation</p>";
            }
        }
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ Error</h4>";
        echo "<p>contract_type column not found in employees table!</p>";
        echo "</div>";
    }
    
    // Show current employment type statistics
    echo "<h3>📊 Current Employment Type Distribution</h3>";
    
    $stmt = $db->query("
        SELECT 
            COALESCE(contract_type, 'NULL') as contract_type,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM employees), 2) as percentage
        FROM employees 
        GROUP BY contract_type 
        ORDER BY count DESC
    ");
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($stats)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>Contract Type</th><th>Count</th><th>Percentage</th></tr>";
        
        foreach ($stats as $stat) {
            $type = $stat['contract_type'];
            $highlight = ($type === 'contract') ? "style='background: #d4edda;'" : "";
            
            echo "<tr $highlight>";
            echo "<td><strong>$type</strong></td>";
            echo "<td>{$stat['count']}</td>";
            echo "<td>{$stat['percentage']}%</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No employees found in database.</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Verification Failed</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='index.php?page=employees&action=add' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>👤 Test Add Employee</a>";
echo "<a href='index.php?page=employees&action=bulk_import' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>📤 Test Bulk Import</a>";
echo "<a href='test_complete_import.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>🧪 Run Full Test</a>";
echo "</div>";
?>
