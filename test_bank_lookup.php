<?php
/**
 * Simple Bank Code Lookup Test
 * Tests the exact logic that should happen during CSV import
 */

echo "<h2>🧪 Bank Code Lookup Test</h2>";

// Test the bank codes loading function
function loadBankCodesData() {
    $bankCodes = [];
    $filePath = 'bank codes.txt';
    
    if (!file_exists($filePath)) {
        echo "<p style='color: orange;'>⚠️ Bank codes file not found, using fallback data</p>";
        return [
            '01094' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Head Office'],
            '01100' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Moi Avenue Nairobi'],
            '02008' => ['bankName' => 'Standard Chartered Bank Kenya Ltd', 'branchName' => 'Moi Avenue'],
            '03001' => ['bankName' => 'Absa Bank Kenya Plc', 'branchName' => 'Head Office - Vpc'],
            '11000' => ['bankName' => 'Co-operative Bank of Kenya Limited', 'branchName' => 'Head Office'],
            '12000' => ['bankName' => 'National Bank Of Kenya', 'branchName' => 'Central Business Unit']
        ];
    }
    
    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if ($lines === false) {
        echo "<p style='color: red;'>❌ Failed to read bank codes file</p>";
        return [];
    }
    
    echo "<p style='color: green;'>✅ Bank codes file found with " . count($lines) . " lines</p>";
    
    // Skip header line if it exists
    if (!empty($lines) && !preg_match('/^\d/', trim($lines[0]))) {
        array_shift($lines);
        echo "<p>ℹ️ Skipped header line</p>";
    }
    
    foreach ($lines as $lineNum => $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Parse the line: Bank Name Bank Code Branch Code Branch Name
        $parts = preg_split('/\s+/', $line, 4);
        
        if (count($parts) >= 4) {
            $bankName = $parts[0];
            $bankCode = $parts[1];
            $branchCode = $parts[2];
            $branchName = $parts[3];
            
            // Handle multi-word bank names
            if (count($parts) > 4) {
                for ($i = 1; $i < count($parts) - 2; $i++) {
                    if (preg_match('/^\d{2}$/', $parts[$i])) {
                        $bankCode = $parts[$i];
                        $branchCode = $parts[$i + 1];
                        $bankName = implode(' ', array_slice($parts, 0, $i));
                        $branchName = implode(' ', array_slice($parts, $i + 2));
                        break;
                    }
                }
            }
            
            // Ensure bank code has leading zero if needed
            $bankCodeStr = str_pad(strval($bankCode), 2, '0', STR_PAD_LEFT);
            $branchCodeStr = str_pad(strval($branchCode), 3, '0', STR_PAD_LEFT);
            $fullCode = $bankCodeStr . $branchCodeStr;
            
            // Validate codes
            if (preg_match('/^\d{2}$/', $bankCodeStr) && preg_match('/^\d{3}$/', $branchCodeStr)) {
                $bankCodes[$fullCode] = [
                    'bankName' => trim($bankName),
                    'branchName' => trim($branchName)
                ];
                
                // Show first few entries for debugging
                if (count($bankCodes) <= 3) {
                    echo "<p>✅ Parsed: $fullCode = {$bankCodes[$fullCode]['bankName']} - {$bankCodes[$fullCode]['branchName']}</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ Invalid format on line " . ($lineNum + 1) . ": $line</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Insufficient parts on line " . ($lineNum + 1) . ": $line</p>";
        }
    }
    
    return $bankCodes;
}

// Load bank codes
$bankCodesData = loadBankCodesData();
echo "<p style='color: green;'><strong>✅ Total bank codes loaded: " . count($bankCodesData) . "</strong></p>";

// Test different CSV row scenarios
echo "<h3>📋 Testing CSV Row Processing Logic</h3>";

$testRows = [
    // Scenario 1: Only bank code provided (should auto-fill name and branch)
    [
        'name' => 'Only bank code provided',
        'data' => ['bank_code' => '1094', 'bank_name' => '', 'bank_branch' => ''],
        'expected' => 'Auto-fill bank name and branch'
    ],
    // Scenario 2: Bank code with leading zero
    [
        'name' => 'Bank code with leading zero',
        'data' => ['bank_code' => '01094', 'bank_name' => '', 'bank_branch' => ''],
        'expected' => 'Auto-fill bank name and branch'
    ],
    // Scenario 3: Bank code + custom name/branch (should keep custom)
    [
        'name' => 'Bank code + custom name/branch',
        'data' => ['bank_code' => '01094', 'bank_name' => 'Custom Bank', 'bank_branch' => 'Custom Branch'],
        'expected' => 'Keep custom values'
    ],
    // Scenario 4: Invalid bank code
    [
        'name' => 'Invalid bank code',
        'data' => ['bank_code' => '99999', 'bank_name' => '', 'bank_branch' => ''],
        'expected' => 'Leave empty or use CSV values'
    ]
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th>Scenario</th><th>Input Bank Code</th><th>Input Name</th><th>Input Branch</th>";
echo "<th>Final Name</th><th>Final Branch</th><th>Status</th>";
echo "</tr>";

foreach ($testRows as $test) {
    $employee = $test['data'];
    
    // SIMULATE THE EXACT LOGIC FROM THE IMPORT FUNCTION
    $bankCode = null;
    $bankName = null;
    $bankBranch = null;
    
    if (!empty($employee['bank_code'])) {
        $bankCode = strval(str_pad(trim($employee['bank_code']), 5, '0', STR_PAD_LEFT));
        
        // Auto-populate bank name and branch from KBA data if not provided in CSV
        if (isset($bankCodesData[$bankCode])) {
            $bankName = !empty($employee['bank_name']) ? trim($employee['bank_name']) : $bankCodesData[$bankCode]['bankName'];
            $bankBranch = !empty($employee['bank_branch']) ? trim($employee['bank_branch']) : $bankCodesData[$bankCode]['branchName'];
            $status = "✅ Found in KBA";
        } else {
            // Use CSV data if bank code not found in KBA data
            $bankName = !empty($employee['bank_name']) ? trim($employee['bank_name']) : null;
            $bankBranch = !empty($employee['bank_branch']) ? trim($employee['bank_branch']) : null;
            $status = "❌ Not found in KBA";
        }
    } else {
        $status = "⚠️ No bank code";
    }
    
    echo "<tr>";
    echo "<td><strong>{$test['name']}</strong></td>";
    echo "<td>{$employee['bank_code']}</td>";
    echo "<td>" . ($employee['bank_name'] ?: '<em>empty</em>') . "</td>";
    echo "<td>" . ($employee['bank_branch'] ?: '<em>empty</em>') . "</td>";
    echo "<td><strong>" . ($bankName ?: '<em>null</em>') . "</strong></td>";
    echo "<td><strong>" . ($bankBranch ?: '<em>null</em>') . "</strong></td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";

// Show available bank codes for reference
echo "<h3>📚 Available Bank Codes (First 10)</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Code</th><th>Bank Name</th><th>Branch Name</th></tr>";
$count = 0;
foreach ($bankCodesData as $code => $data) {
    if ($count >= 10) break;
    echo "<tr>";
    echo "<td><strong>$code</strong></td>";
    echo "<td>{$data['bankName']}</td>";
    echo "<td>{$data['branchName']}</td>";
    echo "</tr>";
    $count++;
}
echo "</table>";

echo "<h3>🎯 Next Steps</h3>";
echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>If the logic above works correctly:</strong></p>";
echo "<ol>";
echo "<li>The bank lookup function is working</li>";
echo "<li>The issue is in the actual import process</li>";
echo "<li>Check the debug logs when doing a real import</li>";
echo "</ol>";
echo "<p><strong>If the logic above fails:</strong></p>";
echo "<ol>";
echo "<li>The bank codes file format might be wrong</li>";
echo "<li>The bank code parsing logic needs adjustment</li>";
echo "<li>The bank codes data structure is incorrect</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='debug_bank_import.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔍 Full Debug</a>";
echo "<a href='index.php?page=employees&action=bulk_import' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>📤 Test Import</a>";
echo "</div>";
?>
