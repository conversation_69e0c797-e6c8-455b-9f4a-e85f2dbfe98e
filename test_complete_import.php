<?php
/**
 * Complete Import Test
 * Tests both employment types and bank code auto-population together
 */

session_start();
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('❌ Database connection failed.');
}

echo "<h2>🧪 Complete Import Feature Test</h2>";
echo "<p>Testing both employment types and bank code auto-population</p>";

// Check if user is logged in and has company_id
if (!isset($_SESSION['company_id'])) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
    echo "<h4>⚠️ Session Required</h4>";
    echo "<p>Please log in to test the import functionality.</p>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Login</a>";
    echo "</div>";
    exit;
}

$companyId = $_SESSION['company_id'];

// Include the functions from employees.php
function getEmploymentTypes($db, $companyId) {
    try {
        $stmt = $db->prepare("SELECT type_name, description, is_default FROM employment_types WHERE company_id = ? AND is_active = 1 ORDER BY is_default DESC, type_name ASC");
        $stmt->execute([$companyId]);
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($types)) {
            return [
                ['type_name' => 'contract', 'description' => 'Contract Employee', 'is_default' => 1],
                ['type_name' => 'permanent', 'description' => 'Permanent Employee', 'is_default' => 0],
                ['type_name' => 'casual', 'description' => 'Casual Employee', 'is_default' => 0],
                ['type_name' => 'intern', 'description' => 'Intern', 'is_default' => 0]
            ];
        }
        
        return $types;
    } catch (Exception $e) {
        return [
            ['type_name' => 'contract', 'description' => 'Contract Employee', 'is_default' => 1],
            ['type_name' => 'permanent', 'description' => 'Permanent Employee', 'is_default' => 0],
            ['type_name' => 'casual', 'description' => 'Casual Employee', 'is_default' => 0],
            ['type_name' => 'intern', 'description' => 'Intern', 'is_default' => 0]
        ];
    }
}

function getDefaultEmploymentType($db, $companyId) {
    try {
        $stmt = $db->prepare("SELECT type_name FROM employment_types WHERE company_id = ? AND is_default = 1 AND is_active = 1 LIMIT 1");
        $stmt->execute([$companyId]);
        $result = $stmt->fetchColumn();
        return $result ?: 'contract';
    } catch (Exception $e) {
        return 'contract';
    }
}

function loadBankCodesData() {
    $bankCodes = [];
    $filePath = 'bank codes.txt';
    
    if (!file_exists($filePath)) {
        return [
            '01094' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Head Office'],
            '01340' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Karen Waterfront Platinum'],
            '02008' => ['bankName' => 'Standard Chartered Bank Kenya Ltd', 'branchName' => 'Moi Avenue']
        ];
    }
    
    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if ($lines === false) {
        return [];
    }
    
    // Skip header line
    array_shift($lines);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        $parts = preg_split('/\s+/', $line);
        
        if (count($parts) >= 4) {
            $bankCode = null;
            $branchCode = null;
            $bankCodeIndex = -1;
            
            for ($i = 1; $i < count($parts) - 1; $i++) {
                if (preg_match('/^\d{2}$/', $parts[$i]) && isset($parts[$i + 1]) && preg_match('/^\d{1,3}$/', $parts[$i + 1])) {
                    $bankCode = $parts[$i];
                    $branchCode = $parts[$i + 1];
                    $bankCodeIndex = $i;
                    break;
                }
            }
            
            if ($bankCode && $branchCode && $bankCodeIndex > 0) {
                $bankName = implode(' ', array_slice($parts, 0, $bankCodeIndex));
                $branchName = implode(' ', array_slice($parts, $bankCodeIndex + 2));
                
                $bankCodeStr = str_pad(strval($bankCode), 2, '0', STR_PAD_LEFT);
                $branchCodeStr = str_pad(strval($branchCode), 3, '0', STR_PAD_LEFT);
                $fullCode = $bankCodeStr . $branchCodeStr;
                
                if (preg_match('/^\d{2}$/', $bankCodeStr) && preg_match('/^\d{3}$/', $branchCodeStr)) {
                    $bankCodes[$fullCode] = [
                        'bankName' => trim($bankName),
                        'branchName' => trim($branchName)
                    ];
                }
            }
        }
    }
    
    return $bankCodes;
}

// Test 1: Employment Types
echo "<h3>🏢 Test 1: Employment Types</h3>";

$employmentTypes = getEmploymentTypes($db, $companyId);
$defaultType = getDefaultEmploymentType($db, $companyId);

echo "<p><strong>Company ID:</strong> $companyId</p>";
echo "<p><strong>Default Employment Type:</strong> <span style='background: #28a745; color: white; padding: 2px 8px; border-radius: 3px;'>$defaultType</span></p>";

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'><th>Type</th><th>Description</th><th>Default</th></tr>";
foreach ($employmentTypes as $type) {
    $defaultBadge = $type['is_default'] ? "✅" : "";
    echo "<tr>";
    echo "<td><strong>{$type['type_name']}</strong></td>";
    echo "<td>{$type['description']}</td>";
    echo "<td>$defaultBadge</td>";
    echo "</tr>";
}
echo "</table>";

// Test 2: Bank Codes
echo "<h3>🏦 Test 2: Bank Codes</h3>";

$bankCodesData = loadBankCodesData();
echo "<p><strong>Bank codes loaded:</strong> " . count($bankCodesData) . "</p>";

$testBankCodes = ['01094', '01340', '02008'];
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'><th>Code</th><th>Bank Name</th><th>Branch Name</th><th>Status</th></tr>";
foreach ($testBankCodes as $code) {
    if (isset($bankCodesData[$code])) {
        echo "<tr>";
        echo "<td><strong>$code</strong></td>";
        echo "<td>{$bankCodesData[$code]['bankName']}</td>";
        echo "<td>{$bankCodesData[$code]['branchName']}</td>";
        echo "<td style='color: green;'>✅ Found</td>";
        echo "</tr>";
    } else {
        echo "<tr>";
        echo "<td><strong>$code</strong></td>";
        echo "<td colspan='2'>Not found</td>";
        echo "<td style='color: red;'>❌ Missing</td>";
        echo "</tr>";
    }
}
echo "</table>";

// Test 3: Combined Logic Simulation
echo "<h3>🔄 Test 3: Combined Import Logic Simulation</h3>";

$testEmployees = [
    [
        'name' => 'John Doe (No employment type, bank code 01094)',
        'data' => [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'employment_type' => '',
            'bank_code' => '01094',
            'bank_name' => '',
            'bank_branch' => ''
        ]
    ],
    [
        'name' => 'Jane Smith (Permanent type, bank code 01340)',
        'data' => [
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'employment_type' => 'permanent',
            'bank_code' => '01340',
            'bank_name' => '',
            'bank_branch' => ''
        ]
    ],
    [
        'name' => 'Bob Wilson (Contract type, custom bank info)',
        'data' => [
            'first_name' => 'Bob',
            'last_name' => 'Wilson',
            'employment_type' => 'contract',
            'bank_code' => '02008',
            'bank_name' => 'Custom Bank Name',
            'bank_branch' => 'Custom Branch'
        ]
    ]
];

echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th>Employee</th><th>Input Type</th><th>Final Type</th><th>Input Bank Code</th><th>Final Bank Name</th><th>Final Branch</th>";
echo "</tr>";

foreach ($testEmployees as $test) {
    $employee = $test['data'];
    
    // Employment type logic
    $validEmploymentTypes = array_column($employmentTypes, 'type_name');
    $contractType = !empty($employee['employment_type']) ? strtolower($employee['employment_type']) : $defaultType;
    if (!in_array($contractType, $validEmploymentTypes)) {
        $contractType = $defaultType;
    }
    
    // Bank code logic
    $bankCode = null;
    $bankName = null;
    $bankBranch = null;
    
    if (!empty($employee['bank_code'])) {
        $bankCode = strval(str_pad(trim($employee['bank_code']), 5, '0', STR_PAD_LEFT));
        
        if (isset($bankCodesData[$bankCode])) {
            $bankName = !empty($employee['bank_name']) ? trim($employee['bank_name']) : $bankCodesData[$bankCode]['bankName'];
            $bankBranch = !empty($employee['bank_branch']) ? trim($employee['bank_branch']) : $bankCodesData[$bankCode]['branchName'];
        } else {
            $bankName = !empty($employee['bank_name']) ? trim($employee['bank_name']) : null;
            $bankBranch = !empty($employee['bank_branch']) ? trim($employee['bank_branch']) : null;
        }
    }
    
    echo "<tr>";
    echo "<td><strong>{$employee['first_name']} {$employee['last_name']}</strong></td>";
    echo "<td>" . ($employee['employment_type'] ?: '<em>empty</em>') . "</td>";
    echo "<td><strong>$contractType</strong></td>";
    echo "<td>{$employee['bank_code']}</td>";
    echo "<td><strong>" . ($bankName ?: '<em>null</em>') . "</strong></td>";
    echo "<td><strong>" . ($bankBranch ?: '<em>null</em>') . "</strong></td>";
    echo "</tr>";
}
echo "</table>";

// Test 4: Current Database State
echo "<h3>📊 Test 4: Current Database State</h3>";

try {
    $stmt = $db->prepare("
        SELECT employee_number, first_name, last_name, contract_type, bank_code, bank_name, bank_branch 
        FROM employees 
        WHERE company_id = ? 
        ORDER BY id DESC 
        LIMIT 5
    ");
    $stmt->execute([$companyId]);
    $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($employees)) {
        echo "<p>No employees found in database for this company.</p>";
    } else {
        echo "<p><strong>Last 5 employees in database:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>Employee #</th><th>Name</th><th>Employment Type</th><th>Bank Code</th><th>Bank Name</th><th>Branch</th>";
        echo "</tr>";
        
        foreach ($employees as $emp) {
            $typeStatus = $emp['contract_type'] ? "✅" : "❌ NULL";
            $bankNameStatus = $emp['bank_name'] ? "✅" : "❌ NULL";
            $branchStatus = $emp['bank_branch'] ? "✅" : "❌ NULL";
            
            echo "<tr>";
            echo "<td>{$emp['employee_number']}</td>";
            echo "<td>{$emp['first_name']} {$emp['last_name']}</td>";
            echo "<td>{$emp['contract_type']} $typeStatus</td>";
            echo "<td>{$emp['bank_code']}</td>";
            echo "<td>{$emp['bank_name']} $bankNameStatus</td>";
            echo "<td>{$emp['bank_branch']} $branchStatus</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error querying employees: " . $e->getMessage() . "</p>";
}

// Summary
echo "<h3>📋 Summary & Next Steps</h3>";

$employmentTypesWorking = !empty($employmentTypes) && $defaultType === 'contract';
$bankCodesWorking = isset($bankCodesData['01094']) && isset($bankCodesData['01340']);

echo "<div style='background: " . ($employmentTypesWorking && $bankCodesWorking ? "#d4edda" : "#fff3cd") . "; border: 1px solid " . ($employmentTypesWorking && $bankCodesWorking ? "#c3e6cb" : "#ffeaa7") . "; padding: 15px; border-radius: 5px;'>";

if ($employmentTypesWorking && $bankCodesWorking) {
    echo "<h4>🎉 All Systems Ready!</h4>";
    echo "<p>Both employment types and bank code auto-population are working correctly.</p>";
} else {
    echo "<h4>⚠️ Issues Detected</h4>";
    if (!$employmentTypesWorking) {
        echo "<p>❌ Employment types issue: Default should be 'contract' and types should be loaded from database</p>";
    }
    if (!$bankCodesWorking) {
        echo "<p>❌ Bank codes issue: Bank codes file not loading properly</p>";
    }
}

echo "<p><strong>Test Results:</strong></p>";
echo "<ul>";
echo "<li>Employment Types: " . ($employmentTypesWorking ? "✅ Working" : "❌ Issues") . "</li>";
echo "<li>Bank Code Lookup: " . ($bankCodesWorking ? "✅ Working" : "❌ Issues") . "</li>";
echo "<li>Default Employment Type: " . ($defaultType === 'contract' ? "✅ Contract" : "❌ Not Contract") . "</li>";
echo "</ul>";

echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='index.php?page=employees&action=bulk_import' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📤 Test Real Import</a>";
echo "<a href='index.php?page=employees&action=add' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>👤 Test Add Employee</a>";
echo "</div>";
?>
