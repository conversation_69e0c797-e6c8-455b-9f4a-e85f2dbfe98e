<?php
/**
 * Test Bank Code String Handling and Leading Zero Preservation
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>🔢 Bank Code String Handling Test</h2>";

// Test 1: API Response String Handling
echo "<h3>🌐 API String Handling Test</h3>";

try {
    // Test the API endpoint
    $apiUrl = 'get_bank_codes.php';
    $apiResponse = file_get_contents('http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $apiUrl);
    
    if ($apiResponse) {
        $apiData = json_decode($apiResponse, true);
        
        if ($apiData && is_array($apiData)) {
            echo "<p style='color: green;'>✅ API returns " . count($apiData) . " bank codes</p>";
            
            // Test specific codes with leading zeros
            $testCodes = ['01094', '02008', '03001', '07101', '11000'];
            $foundCodes = [];
            $missingCodes = [];
            
            foreach ($testCodes as $testCode) {
                if (isset($apiData[$testCode])) {
                    $foundCodes[] = $testCode;
                    $bankData = $apiData[$testCode];
                    
                    // Check if the code is properly formatted as string
                    $isString = is_string($bankData['fullCode']);
                    $hasLeadingZero = strlen($bankData['fullCode']) === 5 && $bankData['fullCode'][0] === '0';
                    
                    echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
                    echo "<strong>Code: $testCode</strong><br>";
                    echo "Full Code: " . $bankData['fullCode'] . " (" . gettype($bankData['fullCode']) . ")<br>";
                    echo "Bank Code: " . $bankData['bankCode'] . " (" . gettype($bankData['bankCode']) . ")<br>";
                    echo "Branch Code: " . $bankData['branchCode'] . " (" . gettype($bankData['branchCode']) . ")<br>";
                    echo "Bank Name: " . htmlspecialchars($bankData['bankName']) . "<br>";
                    echo "Branch Name: " . htmlspecialchars($bankData['branchName']) . "<br>";
                    
                    if ($isString && $hasLeadingZero) {
                        echo "<span style='color: green;'>✅ Properly formatted as string with leading zero</span>";
                    } elseif ($isString) {
                        echo "<span style='color: orange;'>⚠️ String format but no leading zero issue</span>";
                    } else {
                        echo "<span style='color: red;'>❌ Not stored as string - leading zeros may be lost</span>";
                    }
                    echo "</div>";
                } else {
                    $missingCodes[] = $testCode;
                }
            }
            
            if (!empty($missingCodes)) {
                echo "<p style='color: orange;'>⚠️ Missing test codes: " . implode(', ', $missingCodes) . "</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ API returns invalid JSON</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to fetch API response</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ API test failed: " . $e->getMessage() . "</p>";
}

// Test 2: Database Storage Test
echo "<h3>🗄️ Database Storage Test</h3>";

try {
    $db = $database->getConnection();
    
    // Test inserting a bank code with leading zero
    $testBankCode = '01094';
    $testEmployeeData = [
        'company_id' => $_SESSION['company_id'] ?? 1,
        'employee_number' => 'TEST001',
        'first_name' => 'Test',
        'last_name' => 'Employee',
        'basic_salary' => 50000,
        'bank_code' => $testBankCode,
        'bank_name' => 'Kenya Commercial Bank Limited',
        'bank_branch' => 'Head Office'
    ];
    
    // Check if test employee already exists
    $stmt = $db->prepare("SELECT id, bank_code FROM employees WHERE employee_number = ? AND company_id = ?");
    $stmt->execute(['TEST001', $_SESSION['company_id'] ?? 1]);
    $existingEmployee = $stmt->fetch();
    
    if ($existingEmployee) {
        echo "<p style='color: blue;'>ℹ️ Test employee already exists</p>";
        echo "<p>Stored bank code: <strong>" . $existingEmployee['bank_code'] . "</strong> (" . gettype($existingEmployee['bank_code']) . ")</p>";
        
        if ($existingEmployee['bank_code'] === $testBankCode) {
            echo "<p style='color: green;'>✅ Bank code stored correctly with leading zero</p>";
        } else {
            echo "<p style='color: red;'>❌ Bank code stored incorrectly: expected '$testBankCode', got '" . $existingEmployee['bank_code'] . "'</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ No test employee found - would need to create one to test storage</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database test failed: " . $e->getMessage() . "</p>";
}

// Test 3: CSV Template Test
echo "<h3>📄 CSV Template Test</h3>";

try {
    // Capture CSV output
    ob_start();
    $_GET['type'] = 'employees';
    $_GET['format'] = 'csv';
    include 'download_template.php';
    $csvContent = ob_get_clean();
    
    if ($csvContent) {
        $lines = explode("\n", $csvContent);
        $headers = str_getcsv($lines[0]);
        
        echo "<p style='color: green;'>✅ CSV template generated successfully</p>";
        echo "<p>Headers: " . implode(', ', $headers) . "</p>";
        
        // Check sample data for bank codes
        if (count($lines) > 1) {
            for ($i = 1; $i < min(4, count($lines)); $i++) {
                if (!empty(trim($lines[$i]))) {
                    $data = str_getcsv($lines[$i]);
                    $bankCodeIndex = array_search('bank_code', $headers);
                    
                    if ($bankCodeIndex !== false && isset($data[$bankCodeIndex])) {
                        $bankCode = $data[$bankCodeIndex];
                        echo "<p>Sample row $i bank code: <strong>$bankCode</strong> (length: " . strlen($bankCode) . ")</p>";
                        
                        if (strlen($bankCode) === 5 && $bankCode[0] === '0') {
                            echo "<p style='color: green;'>✅ Leading zero preserved in CSV</p>";
                        } else {
                            echo "<p style='color: red;'>❌ Leading zero may be lost in CSV</p>";
                        }
                    }
                }
            }
        }
    } else {
        echo "<p style='color: red;'>❌ Failed to generate CSV template</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ CSV test failed: " . $e->getMessage() . "</p>";
}

// Test 4: JavaScript String Handling Test
echo "<h3>🔧 JavaScript String Handling Test</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🧪 Test Bank Code Formatting:</h4>";
echo "<input type='text' id='testBankCodeInput' placeholder='Enter bank code (e.g., 1094, 01094)' style='padding: 8px; width: 300px; margin-right: 10px;'>";
echo "<button onclick='testBankCodeFormatting()' style='padding: 8px 15px;'>Test Formatting</button>";
echo "<div id='formatTestResult' style='margin-top: 10px;'></div>";
echo "</div>";

echo "<script>";
echo "function testBankCodeFormatting() {";
echo "    const input = document.getElementById('testBankCodeInput');";
echo "    const result = document.getElementById('formatTestResult');";
echo "    const code = input.value.trim();";
echo "    ";
echo "    let formattedCode = code.replace(/[^0-9]/g, '').substring(0, 5);";
echo "    if (formattedCode.length > 0 && formattedCode.length <= 5) {";
echo "        formattedCode = formattedCode.padStart(5, '0');";
echo "    }";
echo "    ";
echo "    result.innerHTML = `";
echo "        <p><strong>Original:</strong> '${code}' (${typeof code})</p>";
echo "        <p><strong>Formatted:</strong> '${formattedCode}' (${typeof formattedCode})</p>";
echo "        <p><strong>Length:</strong> ${formattedCode.length}</p>";
echo "        <p><strong>Has Leading Zero:</strong> ${formattedCode[0] === '0' ? 'Yes' : 'No'}</p>";
echo "        <p style='color: ${formattedCode.length === 5 && formattedCode[0] === '0' ? 'green' : 'red'};'>`;
echo "        ${formattedCode.length === 5 && formattedCode[0] === '0' ? '✅ Properly formatted' : '❌ Formatting issue'}";
echo "        </p>`;";
echo "}";
echo "</script>";

// Test 5: Recommendations
echo "<h3>💡 Recommendations</h3>";

echo "<div style='background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🔧 String Handling Best Practices:</h4>";
echo "<ul>";
echo "<li><strong>Database:</strong> Use VARCHAR fields for bank codes, not INTEGER</li>";
echo "<li><strong>PHP:</strong> Use strval() and str_pad() to ensure string format</li>";
echo "<li><strong>JavaScript:</strong> Use padStart() to add leading zeros</li>";
echo "<li><strong>CSV:</strong> Quote bank codes to preserve leading zeros</li>";
echo "<li><strong>Validation:</strong> Always validate as string, not number</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>⚠️ Common Issues:</h4>";
echo "<ul>";
echo "<li>Excel may remove leading zeros when opening CSV files</li>";
echo "<li>JSON parsing may convert numeric strings to numbers</li>";
echo "<li>Database INTEGER fields will strip leading zeros</li>";
echo "<li>JavaScript parseInt() will remove leading zeros</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<p><a href='index.php?page=employees&action=add'>Test Employee Form</a> | ";
echo "<a href='get_bank_codes.php' target='_blank'>View Bank Codes API</a> | ";
echo "<a href='download_template.php?type=employees&format=csv'>Download CSV Template</a></p>";
?>
