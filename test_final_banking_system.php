<?php
/**
 * Final Banking System Test - String Handling and Leading Zero Preservation
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>🏦 Final Banking System Test - String Handling</h2>";

// Test bank codes with leading zeros
$testCodes = [
    '01094' => 'Kenya Commercial Bank Limited - Head Office',
    '02008' => 'Standard Chartered Bank Kenya Ltd - Moi Avenue',
    '03001' => 'Absa Bank Kenya Plc - Head Office - Vpc',
    '07101' => 'NCBA Bank Kenya Plc - City Centre',
    '11000' => 'Co-operative Bank of Kenya Limited - Head Office'
];

echo "<h3>🔢 Leading Zero Preservation Test</h3>";

// Test 1: API Response
echo "<h4>1. API Response Test</h4>";
try {
    $apiResponse = file_get_contents('http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/get_bank_codes.php');
    $apiData = json_decode($apiResponse, true);
    
    if ($apiData) {
        $passedTests = 0;
        $totalTests = count($testCodes);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>Code</th><th>Type</th><th>Length</th><th>Leading Zero</th><th>Status</th></tr>";
        
        foreach ($testCodes as $code => $description) {
            if (isset($apiData[$code])) {
                $bankData = $apiData[$code];
                $isString = is_string($bankData['fullCode']);
                $correctLength = strlen($bankData['fullCode']) === 5;
                $hasLeadingZero = $bankData['fullCode'][0] === '0';
                $passed = $isString && $correctLength && $hasLeadingZero;
                
                if ($passed) $passedTests++;
                
                echo "<tr>";
                echo "<td><strong>$code</strong></td>";
                echo "<td>" . gettype($bankData['fullCode']) . "</td>";
                echo "<td>" . strlen($bankData['fullCode']) . "</td>";
                echo "<td>" . ($hasLeadingZero ? 'Yes' : 'No') . "</td>";
                echo "<td style='color: " . ($passed ? 'green' : 'red') . ";'>" . ($passed ? '✅ Pass' : '❌ Fail') . "</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
        
        echo "<p><strong>API Test Result: $passedTests/$totalTests tests passed</strong></p>";
        
    } else {
        echo "<p style='color: red;'>❌ Failed to load API data</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ API test failed: " . $e->getMessage() . "</p>";
}

// Test 2: Form Input Simulation
echo "<h4>2. Form Input Simulation</h4>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Test entering bank codes with and without leading zeros:</strong></p>";

$inputTests = [
    '1094' => '01094',
    '01094' => '01094',
    '2008' => '02008',
    '02008' => '02008',
    '11000' => '11000'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'><th>Input</th><th>Expected Output</th><th>JavaScript Result</th><th>Status</th></tr>";

foreach ($inputTests as $input => $expected) {
    echo "<tr>";
    echo "<td>$input</td>";
    echo "<td>$expected</td>";
    echo "<td id='result_$input'>Testing...</td>";
    echo "<td id='status_$input'>...</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Test 3: Database Field Check
echo "<h4>3. Database Field Type Check</h4>";
try {
    $db = $database->getConnection();
    $stmt = $db->query("DESCRIBE employees");
    $columns = $stmt->fetchAll();
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'bank_code') {
            $type = $column['Type'];
            echo "<p><strong>bank_code field type:</strong> <code>$type</code></p>";
            
            if (strpos(strtolower($type), 'varchar') !== false) {
                echo "<p style='color: green;'>✅ VARCHAR field - will preserve leading zeros</p>";
            } elseif (strpos(strtolower($type), 'char') !== false) {
                echo "<p style='color: green;'>✅ CHAR field - will preserve leading zeros</p>";
            } elseif (strpos(strtolower($type), 'text') !== false) {
                echo "<p style='color: green;'>✅ TEXT field - will preserve leading zeros</p>";
            } else {
                echo "<p style='color: red;'>❌ Non-string field - may lose leading zeros</p>";
            }
            break;
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database check failed: " . $e->getMessage() . "</p>";
}

// Test 4: CSV Template Check
echo "<h4>4. CSV Template Check</h4>";
echo "<p><a href='download_template.php?type=employees&format=csv' target='_blank'>Download CSV Template</a> and check if bank codes have leading zeros preserved.</p>";

// Summary and Recommendations
echo "<h3>📋 Summary and Recommendations</h3>";

echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>✅ String Handling Improvements Made:</h4>";
echo "<ul>";
echo "<li><strong>API Endpoint:</strong> Uses strval() and str_pad() to ensure string format</li>";
echo "<li><strong>Database Storage:</strong> Bank codes stored as VARCHAR strings</li>";
echo "<li><strong>Form Validation:</strong> JavaScript padStart() preserves leading zeros</li>";
echo "<li><strong>CSV Template:</strong> Bank codes properly formatted as strings</li>";
echo "<li><strong>Bulk Import:</strong> str_pad() ensures 5-digit format with leading zeros</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>⚠️ Important Notes:</h4>";
echo "<ul>";
echo "<li><strong>Excel Handling:</strong> When opening CSV in Excel, bank codes may lose leading zeros unless formatted as text</li>";
echo "<li><strong>Data Entry:</strong> Users can enter codes with or without leading zeros - system will format correctly</li>";
echo "<li><strong>Validation:</strong> All bank codes are validated as 5-digit strings</li>";
echo "<li><strong>Storage:</strong> Database stores codes as VARCHAR to preserve leading zeros</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>🎯 Usage Instructions:</h4>";
echo "<ol>";
echo "<li><strong>Manual Entry:</strong> Enter 5-digit bank codes (e.g., 01094, 02008)</li>";
echo "<li><strong>CSV Import:</strong> Use 5-digit codes in CSV files</li>";
echo "<li><strong>Auto-completion:</strong> System will suggest codes as you type</li>";
echo "<li><strong>Validation:</strong> Invalid codes will show error messages</li>";
echo "<li><strong>Auto-population:</strong> Bank and branch names fill automatically</li>";
echo "</ol>";
echo "</div>";

// JavaScript for form simulation
echo "<script>";
echo "function testBankCodeFormatting() {";
foreach ($inputTests as $input => $expected) {
    echo "    let input$input = '$input';";
    echo "    let formatted$input = input$input.replace(/[^0-9]/g, '').substring(0, 5);";
    echo "    if (formatted$input.length > 0 && formatted$input.length <= 5) {";
    echo "        formatted$input = formatted$input.padStart(5, '0');";
    echo "    }";
    echo "    document.getElementById('result_$input').textContent = formatted$input;";
    echo "    document.getElementById('status_$input').innerHTML = formatted$input === '$expected' ? '<span style=\"color: green;\">✅ Pass</span>' : '<span style=\"color: red;\">❌ Fail</span>';";
}
echo "}";
echo "// Run test on page load";
echo "document.addEventListener('DOMContentLoaded', testBankCodeFormatting);";
echo "</script>";

echo "<hr>";
echo "<p><a href='index.php?page=employees&action=add'>Test Employee Form</a> | ";
echo "<a href='get_bank_codes.php' target='_blank'>View Bank Codes API</a> | ";
echo "<a href='test_bank_code_strings.php'>Detailed String Tests</a></p>";
?>
