<?php
/**
 * Setup Employment Types Table
 * 
 * This script creates the employment_types table and populates it with default values
 * for all existing companies, making employment types configurable per company.
 */

session_start();
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('❌ Database connection failed.');
}

echo "<h2>🏢 Setting Up Employment Types</h2>";

try {
    // Step 1: Create employment_types table
    echo "<h3>📋 Step 1: Creating employment_types table</h3>";
    
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS employment_types (
            id INT PRIMARY KEY AUTO_INCREMENT,
            company_id INT NOT NULL,
            type_name VARCHAR(50) NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            is_default BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
            UNIQUE KEY unique_type_per_company (company_id, type_name)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($createTableSQL);
    echo "<p style='color: green;'>✅ Employment types table created successfully</p>";
    
    // Step 2: Get all companies
    echo "<h3>🏢 Step 2: Getting existing companies</h3>";
    
    $stmt = $db->query("SELECT id, name FROM companies ORDER BY id");
    $companies = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✅ Found " . count($companies) . " companies</p>";
    
    if (empty($companies)) {
        echo "<p style='color: orange;'>⚠️ No companies found. Please create a company first.</p>";
        exit;
    }
    
    // Step 3: Insert default employment types for each company
    echo "<h3>📝 Step 3: Inserting default employment types</h3>";
    
    $defaultTypes = [
        ['type_name' => 'contract', 'description' => 'Contract Employee - Fixed term employment', 'is_default' => true],
        ['type_name' => 'permanent', 'description' => 'Permanent Employee - Indefinite term employment', 'is_default' => false],
        ['type_name' => 'casual', 'description' => 'Casual Employee - Temporary or part-time employment', 'is_default' => false],
        ['type_name' => 'intern', 'description' => 'Intern - Training or learning position', 'is_default' => false],
        ['type_name' => 'consultant', 'description' => 'Consultant - Professional services contractor', 'is_default' => false],
        ['type_name' => 'probation', 'description' => 'Probationary Employee - Under evaluation period', 'is_default' => false]
    ];
    
    $insertStmt = $db->prepare("
        INSERT IGNORE INTO employment_types (company_id, type_name, description, is_default) 
        VALUES (?, ?, ?, ?)
    ");
    
    $totalInserted = 0;
    
    foreach ($companies as $company) {
        echo "<p><strong>Company: {$company['name']} (ID: {$company['id']})</strong></p>";
        echo "<ul>";
        
        foreach ($defaultTypes as $type) {
            $result = $insertStmt->execute([
                $company['id'],
                $type['type_name'],
                $type['description'],
                $type['is_default'] ? 1 : 0
            ]);
            
            if ($insertStmt->rowCount() > 0) {
                $status = "✅ Added";
                $totalInserted++;
            } else {
                $status = "ℹ️ Already exists";
            }
            
            $defaultText = $type['is_default'] ? " (DEFAULT)" : "";
            echo "<li>{$type['type_name']}{$defaultText} - $status</li>";
        }
        echo "</ul>";
    }
    
    echo "<p style='color: green;'><strong>✅ Total employment types inserted: $totalInserted</strong></p>";
    
    // Step 4: Verify the setup
    echo "<h3>🔍 Step 4: Verification</h3>";
    
    foreach ($companies as $company) {
        $stmt = $db->prepare("
            SELECT type_name, description, is_default, is_active 
            FROM employment_types 
            WHERE company_id = ? 
            ORDER BY is_default DESC, type_name ASC
        ");
        $stmt->execute([$company['id']]);
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p><strong>{$company['name']} Employment Types:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr style='background: #f8f9fa;'><th>Type</th><th>Description</th><th>Default</th><th>Active</th></tr>";
        
        foreach ($types as $type) {
            $defaultBadge = $type['is_default'] ? "<span style='background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;'>DEFAULT</span>" : "";
            $activeBadge = $type['is_active'] ? "<span style='color: green;'>✅</span>" : "<span style='color: red;'>❌</span>";
            
            echo "<tr>";
            echo "<td><strong>{$type['type_name']}</strong></td>";
            echo "<td>{$type['description']}</td>";
            echo "<td>$defaultBadge</td>";
            echo "<td>$activeBadge</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 5: Test the functions
    echo "<h3>🧪 Step 5: Testing Functions</h3>";
    
    // Include the functions from employees.php
    function getEmploymentTypes($db, $companyId) {
        try {
            $stmt = $db->prepare("SELECT type_name, description, is_default FROM employment_types WHERE company_id = ? AND is_active = 1 ORDER BY is_default DESC, type_name ASC");
            $stmt->execute([$companyId]);
            $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($types)) {
                return [
                    ['type_name' => 'contract', 'description' => 'Contract Employee', 'is_default' => 1],
                    ['type_name' => 'permanent', 'description' => 'Permanent Employee', 'is_default' => 0],
                    ['type_name' => 'casual', 'description' => 'Casual Employee', 'is_default' => 0],
                    ['type_name' => 'intern', 'description' => 'Intern', 'is_default' => 0]
                ];
            }
            
            return $types;
        } catch (Exception $e) {
            return [
                ['type_name' => 'contract', 'description' => 'Contract Employee', 'is_default' => 1],
                ['type_name' => 'permanent', 'description' => 'Permanent Employee', 'is_default' => 0],
                ['type_name' => 'casual', 'description' => 'Casual Employee', 'is_default' => 0],
                ['type_name' => 'intern', 'description' => 'Intern', 'is_default' => 0]
            ];
        }
    }
    
    function getDefaultEmploymentType($db, $companyId) {
        try {
            $stmt = $db->prepare("SELECT type_name FROM employment_types WHERE company_id = ? AND is_default = 1 AND is_active = 1 LIMIT 1");
            $stmt->execute([$companyId]);
            $result = $stmt->fetchColumn();
            return $result ?: 'contract';
        } catch (Exception $e) {
            return 'contract';
        }
    }
    
    foreach ($companies as $company) {
        $types = getEmploymentTypes($db, $company['id']);
        $defaultType = getDefaultEmploymentType($db, $company['id']);
        
        echo "<p><strong>Function Test for {$company['name']}:</strong></p>";
        echo "<ul>";
        echo "<li>Default employment type: <strong>$defaultType</strong></li>";
        echo "<li>Available types: " . implode(', ', array_column($types, 'type_name')) . "</li>";
        echo "</ul>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🎉 Setup Complete!</h4>";
    echo "<p><strong>What's been configured:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Employment Types Table:</strong> Created with proper foreign key relationships</li>";
    echo "<li>✅ <strong>Default Types:</strong> Contract (default), Permanent, Casual, Intern, Consultant, Probation</li>";
    echo "<li>✅ <strong>Company-Specific:</strong> Each company has its own set of employment types</li>";
    echo "<li>✅ <strong>Default Setting:</strong> 'Contract' is now the default employment type</li>";
    echo "<li>✅ <strong>Database-Driven:</strong> Employment types are now fetched from database, not hardcoded</li>";
    echo "</ul>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Test the employee form to see the new dropdown</li>";
    echo "<li>Test bulk import with employment types</li>";
    echo "<li>Optionally customize employment types per company</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Setup Failed</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='index.php?page=employees' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Go to Employees</a>";
echo "<a href='index.php?page=employees&action=bulk_import' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>📤 Test Import</a>";
echo "</div>";
?>
