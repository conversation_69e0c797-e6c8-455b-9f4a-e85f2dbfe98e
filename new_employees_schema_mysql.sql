-- Updated Employees Table Schema for MySQL
-- Includes all banking fields and robust string handling for bank codes

DROP TABLE IF EXISTS employees;

CREATE TABLE employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    employee_number VARCHAR(20) UNIQUE NOT NULL,
    
    -- Personal Information
    first_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    middle_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    id_number VARCHAR(20),
    date_of_birth DATE,
    gender VARCHAR(10),
    address TEXT,
    
    -- Employment Information
    department_id INT,
    position_id INT,
    hire_date DATE,
    contract_type VARCHAR(20) DEFAULT 'permanent',
    employment_status VARCHAR(20) DEFAULT 'active',
    basic_salary DECIMAL(15,2) DEFAULT 0,
    
    -- Statutory Information (Kenya)
    kra_pin VARCHAR(20),           -- Kenya Revenue Authority PIN
    nssf_number VARCHAR(20),       -- National Social Security Fund
    shif_id VARCHAR(20),           -- Social Health Insurance Fund ID (formerly NHIF)
    
    -- Banking Information (Enhanced for Kenya Banking System)
    bank_code VARCHAR(10),         -- 5-digit bank-branch code (e.g., '01094')
    bank_name VARCHAR(100),        -- Full bank name
    bank_branch VARCHAR(100),      -- Branch name
    bank_account VARCHAR(50),      -- Account number (preserves leading zeros)
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (position_id) REFERENCES job_positions(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Indexes for better performance
CREATE INDEX idx_employees_company_id ON employees(company_id);
CREATE INDEX idx_employees_employee_number ON employees(employee_number);
CREATE INDEX idx_employees_department_id ON employees(department_id);
CREATE INDEX idx_employees_position_id ON employees(position_id);
CREATE INDEX idx_employees_bank_code ON employees(bank_code);
CREATE INDEX idx_employees_email ON employees(email);
CREATE INDEX idx_employees_id_number ON employees(id_number);

-- Comments for documentation
-- bank_code: 5-digit string combining bank code (2 digits) + branch code (3 digits)
-- Examples: '01094' (KCB Head Office), '02008' (Standard Chartered Moi Avenue)
-- bank_account: Account number stored as string to preserve leading zeros
-- All banking fields use VARCHAR to ensure string preservation
