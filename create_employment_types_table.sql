-- =====================================================
-- Employment Types Table Creation
-- =====================================================
-- This creates a configurable employment types table
-- instead of hardcoded values

USE kenyan_payroll;

-- Create employment_types table
CREATE TABLE IF NOT EXISTS employment_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_id INT NOT NULL,
    type_name VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    UNIQUE KEY unique_type_per_company (company_id, type_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default employment types for existing companies
INSERT IGNORE INTO employment_types (company_id, type_name, description, is_default) 
SELECT id, 'contract', 'Contract Employee - Fixed term employment', TRUE FROM companies;

INSERT IGNORE INTO employment_types (company_id, type_name, description, is_default) 
SELECT id, 'permanent', 'Permanent Employee - Indefinite term employment', FALSE FROM companies;

INSERT IGNORE INTO employment_types (company_id, type_name, description, is_default) 
SELECT id, 'casual', 'Casual Employee - Temporary or part-time employment', FALSE FROM companies;

INSERT IGNORE INTO employment_types (company_id, type_name, description, is_default) 
SELECT id, 'intern', 'Intern - Training or learning position', FALSE FROM companies;

INSERT IGNORE INTO employment_types (company_id, type_name, description, is_default) 
SELECT id, 'consultant', 'Consultant - Professional services contractor', FALSE FROM companies;

INSERT IGNORE INTO employment_types (company_id, type_name, description, is_default) 
SELECT id, 'probation', 'Probationary Employee - Under evaluation period', FALSE FROM companies;

-- Update employees table to use employment_type_id (optional migration)
-- ALTER TABLE employees ADD COLUMN employment_type_id INT NULL AFTER contract_type;
-- ALTER TABLE employees ADD FOREIGN KEY (employment_type_id) REFERENCES employment_types(id) ON DELETE SET NULL;

-- For now, we'll keep the contract_type column and sync it with employment_types

-- Verification queries
SELECT 'Employment types created successfully' as result;
SELECT company_id, type_name, is_default FROM employment_types ORDER BY company_id, type_name;
