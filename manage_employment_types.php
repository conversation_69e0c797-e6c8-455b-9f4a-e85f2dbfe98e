<?php
/**
 * Employment Types Management
 * Simple interface to manage employment types per company
 */

session_start();
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('❌ Database connection failed.');
}

// Check if user is logged in
if (!isset($_SESSION['company_id'])) {
    header('Location: index.php');
    exit;
}

$companyId = $_SESSION['company_id'];
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        $typeName = trim($_POST['type_name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $isDefault = isset($_POST['is_default']) ? 1 : 0;
        
        if (!empty($typeName)) {
            try {
                // If setting as default, unset other defaults first
                if ($isDefault) {
                    $stmt = $db->prepare("UPDATE employment_types SET is_default = 0 WHERE company_id = ?");
                    $stmt->execute([$companyId]);
                }
                
                $stmt = $db->prepare("INSERT INTO employment_types (company_id, type_name, description, is_default) VALUES (?, ?, ?, ?)");
                $stmt->execute([$companyId, strtolower($typeName), $description, $isDefault]);
                
                $message = "Employment type '$typeName' added successfully!";
                $messageType = 'success';
            } catch (Exception $e) {
                $message = "Error adding employment type: " . $e->getMessage();
                $messageType = 'danger';
            }
        } else {
            $message = "Type name is required!";
            $messageType = 'warning';
        }
    } elseif ($action === 'update') {
        $id = $_POST['id'] ?? '';
        $isDefault = isset($_POST['is_default']) ? 1 : 0;
        $isActive = isset($_POST['is_active']) ? 1 : 0;
        
        try {
            // If setting as default, unset other defaults first
            if ($isDefault) {
                $stmt = $db->prepare("UPDATE employment_types SET is_default = 0 WHERE company_id = ? AND id != ?");
                $stmt->execute([$companyId, $id]);
            }
            
            $stmt = $db->prepare("UPDATE employment_types SET is_default = ?, is_active = ? WHERE id = ? AND company_id = ?");
            $stmt->execute([$isDefault, $isActive, $id, $companyId]);
            
            $message = "Employment type updated successfully!";
            $messageType = 'success';
        } catch (Exception $e) {
            $message = "Error updating employment type: " . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get current employment types
$stmt = $db->prepare("SELECT * FROM employment_types WHERE company_id = ? ORDER BY is_default DESC, type_name ASC");
$stmt->execute([$companyId]);
$employmentTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get company name
$stmt = $db->prepare("SELECT name FROM companies WHERE id = ?");
$stmt->execute([$companyId]);
$companyName = $stmt->fetchColumn();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Employment Types Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users-cog"></i> Employment Types Management</h2>
                    <a href="index.php?page=employees" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Employees
                    </a>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-building"></i> Company: <?php echo htmlspecialchars($companyName); ?></h5>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Current Employment Types -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Current Employment Types</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($employmentTypes)): ?>
                            <p class="text-muted">No employment types found. Add some below.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Type Name</th>
                                            <th>Description</th>
                                            <th>Default</th>
                                            <th>Active</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($employmentTypes as $type): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo ucfirst(htmlspecialchars($type['type_name'])); ?></strong>
                                                    <?php if ($type['is_default']): ?>
                                                        <span class="badge bg-success ms-2">Default</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($type['description']); ?></td>
                                                <td>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="update">
                                                        <input type="hidden" name="id" value="<?php echo $type['id']; ?>">
                                                        <input type="hidden" name="is_active" value="<?php echo $type['is_active']; ?>">
                                                        <input type="checkbox" name="is_default" <?php echo $type['is_default'] ? 'checked' : ''; ?> 
                                                               onchange="this.form.submit()">
                                                    </form>
                                                </td>
                                                <td>
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="update">
                                                        <input type="hidden" name="id" value="<?php echo $type['id']; ?>">
                                                        <input type="hidden" name="is_default" value="<?php echo $type['is_default']; ?>">
                                                        <input type="checkbox" name="is_active" <?php echo $type['is_active'] ? 'checked' : ''; ?> 
                                                               onchange="this.form.submit()">
                                                    </form>
                                                </td>
                                                <td>
                                                    <span class="text-muted small">
                                                        Created: <?php echo date('M j, Y', strtotime($type['created_at'])); ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Add New Employment Type -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus"></i> Add New Employment Type</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add">
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="type_name" class="form-label">Type Name *</label>
                                        <input type="text" class="form-control" id="type_name" name="type_name" 
                                               placeholder="e.g., freelancer, consultant" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <input type="text" class="form-control" id="description" name="description" 
                                               placeholder="Brief description of this employment type">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <div class="form-check mt-4">
                                            <input type="checkbox" class="form-check-input" id="is_default" name="is_default">
                                            <label class="form-check-label" for="is_default">
                                                Set as Default
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Employment Type
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Usage Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> Usage Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>How Employment Types Work:</h6>
                                <ul class="small">
                                    <li><strong>Default Type:</strong> Used when no employment type is specified in bulk imports</li>
                                    <li><strong>Active Types:</strong> Available in employee forms and bulk imports</li>
                                    <li><strong>Inactive Types:</strong> Hidden from forms but preserved in existing employee records</li>
                                    <li><strong>Company-Specific:</strong> Each company has its own set of employment types</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Common Employment Types:</h6>
                                <ul class="small">
                                    <li><strong>Contract:</strong> Fixed-term employment (recommended default)</li>
                                    <li><strong>Permanent:</strong> Indefinite-term employment</li>
                                    <li><strong>Casual:</strong> Temporary or part-time work</li>
                                    <li><strong>Intern:</strong> Training or learning positions</li>
                                    <li><strong>Consultant:</strong> Professional services contractors</li>
                                    <li><strong>Probation:</strong> Employees under evaluation</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
