<?php
/**
 * Employees Table Schema Migration
 * 
 * This script safely migrates the employees table to the new schema
 * with enhanced banking fields and proper string handling
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    die('<div style="color: red; font-weight: bold;">❌ Access denied. Please log in first.</div>');
}

// Debug session information
echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; font-size: 12px;'>";
echo "<strong>Debug Info:</strong><br>";
echo "User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "<br>";
echo "Role: " . ($_SESSION['role'] ?? 'Not set') . "<br>";
echo "Company ID: " . ($_SESSION['company_id'] ?? 'Not set') . "<br>";
echo "</div>";

// More flexible admin check - allow if user is logged in (you can tighten this later)
if (!isset($_SESSION['user_id'])) {
    die('<div style="color: red; font-weight: bold;">❌ Access denied. User session required.</div>');
}

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('<div style="color: red; font-weight: bold;">❌ Database connection failed.</div>');
}

echo "<h2>🔄 Employees Table Schema Migration</h2>";

$action = $_GET['action'] ?? '';
$confirm = $_GET['confirm'] ?? '';

if ($action === 'migrate' && $confirm === 'yes') {
    try {
        echo "<h3>📋 Starting Migration Process</h3>";
        
        // Step 1: Check current table structure
        echo "<p>1. Checking current table structure...</p>";
        $stmt = $db->query("DESCRIBE employees");
        $currentColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p style='color: green;'>✅ Current table has " . count($currentColumns) . " columns</p>";
        
        // Step 2: Backup existing data
        echo "<p>2. Creating backup of existing data...</p>";
        $backupTableName = 'employees_backup_' . date('Y_m_d_H_i_s');
        
        $stmt = $db->prepare("SELECT COUNT(*) FROM employees WHERE company_id = ?");
        $stmt->execute([$_SESSION['company_id']]);
        $recordCount = $stmt->fetchColumn();
        
        if ($recordCount > 0) {
            $db->exec("CREATE TABLE $backupTableName AS SELECT * FROM employees WHERE company_id = " . $_SESSION['company_id']);
            echo "<p style='color: green;'>✅ Backed up $recordCount records to $backupTableName</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ No records to backup (table is empty)</p>";
        }
        
        // Step 3: Rename old table instead of dropping (safer with foreign keys)
        echo "<p>3. Renaming old employees table to preserve foreign key relationships...</p>";

        $oldTableName = 'employees_old_' . date('Y_m_d_H_i_s');

        try {
            $db->exec("ALTER TABLE employees RENAME TO $oldTableName");
            echo "<p style='color: green;'>✅ Old table renamed to $oldTableName</p>";
        } catch (Exception $e) {
            // If rename fails, try the foreign key approach
            echo "<p style='color: orange;'>⚠️ Rename failed, trying foreign key approach...</p>";

            // Disable foreign key checks temporarily
            $db->exec("SET FOREIGN_KEY_CHECKS = 0");
            echo "<p style='color: blue;'>ℹ️ Disabled foreign key checks temporarily</p>";

            // Drop old table
            $db->exec("DROP TABLE IF EXISTS employees");
            echo "<p style='color: green;'>✅ Old table dropped</p>";
        }
        
        // Step 4: Create new table with enhanced schema
        echo "<p>4. Creating new employees table with enhanced banking schema...</p>";
        
        $newTableSQL = "CREATE TABLE employees (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL,
            employee_number VARCHAR(20) UNIQUE NOT NULL,
            
            -- Personal Information
            first_name VARCHAR(50) NOT NULL,
            middle_name VARCHAR(50),
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            id_number VARCHAR(20),
            date_of_birth DATE,
            gender VARCHAR(10),
            address TEXT,
            
            -- Employment Information
            department_id INTEGER,
            position_id INTEGER,
            hire_date DATE,
            contract_type VARCHAR(20) DEFAULT 'permanent',
            employment_status VARCHAR(20) DEFAULT 'active',
            basic_salary DECIMAL(15,2) DEFAULT 0,
            
            -- Statutory Information (Kenya)
            kra_pin VARCHAR(20),
            nssf_number VARCHAR(20),
            shif_id VARCHAR(20),
            
            -- Banking Information (Enhanced)
            bank_code VARCHAR(10),
            bank_name VARCHAR(100),
            bank_branch VARCHAR(100),
            bank_account VARCHAR(50),
            
            -- Timestamps
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            -- Foreign Key Constraints
            FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
            FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
            FOREIGN KEY (position_id) REFERENCES job_positions(id) ON DELETE SET NULL
        )";
        
        $db->exec($newTableSQL);
        echo "<p style='color: green;'>✅ New employees table created with enhanced banking schema</p>";

        // Re-enable foreign key checks
        $db->exec("SET FOREIGN_KEY_CHECKS = 1");
        echo "<p style='color: blue;'>ℹ️ Re-enabled foreign key checks</p>";
        
        // Step 5: Create indexes
        echo "<p>5. Creating performance indexes...</p>";
        $indexes = [
            "CREATE INDEX idx_employees_company_id ON employees(company_id)",
            "CREATE INDEX idx_employees_employee_number ON employees(employee_number)",
            "CREATE INDEX idx_employees_department_id ON employees(department_id)",
            "CREATE INDEX idx_employees_position_id ON employees(position_id)",
            "CREATE INDEX idx_employees_bank_code ON employees(bank_code)",
            "CREATE INDEX idx_employees_email ON employees(email)",
            "CREATE INDEX idx_employees_id_number ON employees(id_number)"
        ];
        
        foreach ($indexes as $indexSQL) {
            try {
                $db->exec($indexSQL);
            } catch (Exception $e) {
                // Index might already exist, continue
            }
        }
        echo "<p style='color: green;'>✅ Performance indexes created</p>";
        
        // Step 6: Restore data from old table
        if ($recordCount > 0) {
            echo "<p>6. Restoring data from old table...</p>";

            // Determine source table (either renamed table or backup table)
            $sourceTable = isset($oldTableName) ? $oldTableName : $backupTableName;

            // Map old columns to new columns
            $columnMapping = [
                'id' => 'id',
                'company_id' => 'company_id',
                'employee_number' => 'employee_number',
                'first_name' => 'first_name',
                'middle_name' => 'middle_name',
                'last_name' => 'last_name',
                'email' => 'email',
                'phone' => 'phone',
                'id_number' => 'id_number',
                'date_of_birth' => 'date_of_birth',
                'gender' => 'gender',
                'address' => 'address',
                'department_id' => 'department_id',
                'position_id' => 'position_id',
                'hire_date' => 'hire_date',
                'contract_type' => 'contract_type',
                'employment_status' => 'employment_status',
                'basic_salary' => 'basic_salary',
                'kra_pin' => 'kra_pin',
                'nssf_number' => 'nssf_number',
                'nhif_number' => 'shif_id',
                'bank_code' => 'bank_code',
                'bank_name' => 'bank_name',
                'bank_branch' => 'bank_branch',
                'bank_account' => 'bank_account',
                'account_number' => 'bank_account', // Map old account_number to new bank_account
                'created_at' => 'created_at',
                'updated_at' => 'updated_at'
            ];

            // Get data from source table
            $sourceData = $db->query("SELECT * FROM $sourceTable")->fetchAll();
            
            foreach ($sourceData as $row) {
                $insertColumns = [];
                $insertValues = [];
                $insertParams = [];
                
                foreach ($columnMapping as $oldCol => $newCol) {
                    if (isset($row[$oldCol]) && $row[$oldCol] !== null) {
                        $insertColumns[] = $newCol;
                        $insertValues[] = '?';
                        
                        // Special handling for bank_code to ensure string format
                        if ($newCol === 'bank_code' && !empty($row[$oldCol])) {
                            $insertParams[] = str_pad(strval($row[$oldCol]), 5, '0', STR_PAD_LEFT);
                        } else {
                            $insertParams[] = $row[$oldCol];
                        }
                    }
                }
                
                if (!empty($insertColumns)) {
                    $insertSQL = "INSERT INTO employees (" . implode(', ', $insertColumns) . ") VALUES (" . implode(', ', $insertValues) . ")";
                    $stmt = $db->prepare($insertSQL);
                    $stmt->execute($insertParams);
                }
            }
            
            echo "<p style='color: green;'>✅ Restored $recordCount records with enhanced banking data</p>";
        }
        
        // Step 7: Verify migration
        echo "<p>7. Verifying migration...</p>";
        $stmt = $db->prepare("SELECT COUNT(*) FROM employees WHERE company_id = ?");
        $stmt->execute([$_SESSION['company_id']]);
        $newRecordCount = $stmt->fetchColumn();
        
        echo "<p style='color: green;'>✅ Migration completed successfully!</p>";
        echo "<p><strong>Records before:</strong> $recordCount</p>";
        echo "<p><strong>Records after:</strong> $newRecordCount</p>";
        
        if (isset($backupTableName)) {
            echo "<p><strong>Backup table:</strong> $backupTableName</p>";
        }
        if (isset($oldTableName)) {
            echo "<p><strong>Old table renamed to:</strong> $oldTableName</p>";
        }
        
        // Step 8: Optional cleanup of old table
        if (isset($oldTableName) && $newRecordCount === $recordCount) {
            echo "<p>8. Cleaning up old table...</p>";
            try {
                $db->exec("DROP TABLE IF EXISTS $oldTableName");
                echo "<p style='color: green;'>✅ Old table cleaned up successfully</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Old table cleanup failed (table preserved): " . $e->getMessage() . "</p>";
            }
        }

        // Log the activity
        logActivity('employees_schema_migration', "Migrated employees table schema - $newRecordCount records");
        
        echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>🎉 Migration Successful!</h4>";
        echo "<p>The employees table has been successfully migrated to the new schema with enhanced banking fields.</p>";
        echo "<ul>";
        echo "<li>✅ <strong>bank_code</strong>: Now VARCHAR(10) for 5-digit codes with leading zeros</li>";
        echo "<li>✅ <strong>bank_branch</strong>: New field for branch names</li>";
        echo "<li>✅ <strong>bank_account</strong>: Renamed from account_number, preserves leading zeros</li>";
        echo "<li>✅ <strong>middle_name</strong>: Added for complete name handling</li>";
        echo "<li>✅ <strong>Performance indexes</strong>: Added for better query performance</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='index.php?page=employees&action=list' style='background: #006b3f; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Go to Employee Management</a>";
        echo "<a href='test_final_banking_system.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Banking System</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 15px; border: 1px solid #dc3545; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>❌ Migration Failed</h4>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "<p>The migration has been aborted. Your original data should be safe.</p>";
        echo "</div>";
    }
    
} else {
    // Show migration confirmation
    try {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM employees WHERE company_id = ?");
        $stmt->execute([$_SESSION['company_id']]);
        $result = $stmt->fetch();
        $employeeCount = $result['count'];
        
        echo "<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>⚠️ Schema Migration Required</h3>";
        echo "<p>The employees table needs to be updated to support the enhanced banking system with:</p>";
        echo "<ul>";
        echo "<li><strong>Enhanced bank_code field</strong>: VARCHAR(10) to store 5-digit codes with leading zeros</li>";
        echo "<li><strong>New bank_branch field</strong>: VARCHAR(100) for branch names</li>";
        echo "<li><strong>Improved bank_account field</strong>: VARCHAR(50) to preserve leading zeros</li>";
        echo "<li><strong>Added middle_name field</strong>: VARCHAR(50) for complete names</li>";
        echo "<li><strong>Performance indexes</strong>: For better query performance</li>";
        echo "</ul>";
        
        if ($employeeCount > 0) {
            echo "<h4>📊 Current Data:</h4>";
            echo "<p>You have <strong>$employeeCount employees</strong> in your company that will be migrated.</p>";
            echo "<p><strong>⚠️ Important:</strong> A backup will be created before migration.</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ No employee data found - migration will be safe.</p>";
        }
        
        echo "<h4>🔒 Safety Measures:</h4>";
        echo "<ul>";
        echo "<li>✅ Automatic backup creation before migration</li>";
        echo "<li>✅ Data preservation and mapping</li>";
        echo "<li>✅ Rollback capability if needed</li>";
        echo "<li>✅ Verification after migration</li>";
        echo "</ul>";
        
        echo "<h4>⚡ This action will:</h4>";
        echo "<ol>";
        echo "<li>Create a backup of your current employees table</li>";
        echo "<li>Drop the old employees table</li>";
        echo "<li>Create the new enhanced employees table</li>";
        echo "<li>Restore your data with proper field mapping</li>";
        echo "<li>Create performance indexes</li>";
        echo "</ol>";
        
        echo "<p><strong>Are you ready to proceed with the migration?</strong></p>";
        
        echo "<div style='margin-top: 20px;'>";
        echo "<a href='?action=migrate&confirm=yes' onclick='return confirm(\"Are you sure you want to migrate the employees table schema? This will drop and recreate the table with a backup of your data.\")' style='background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin-right: 10px; font-weight: bold;'>🔄 Yes, Migrate Schema</a>";
        echo "<a href='index.php?page=employees&action=list' style='background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;'>Cancel</a>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 15px; border: 1px solid #dc3545; border-radius: 5px;'>";
        echo "<h4>❌ Error</h4>";
        echo "<p>Failed to check current table: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Employees Schema Migration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 50px auto;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        
        h2 {
            color: #006b3f;
            border-bottom: 3px solid #ce1126;
            padding-bottom: 10px;
        }
        
        h3 {
            color: #333;
            margin-top: 30px;
        }
        
        a {
            display: inline-block;
            margin: 5px;
        }
        
        code {
            background: #f1f1f1;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
</body>
</html>
