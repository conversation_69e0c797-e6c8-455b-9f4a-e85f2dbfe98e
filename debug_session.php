<?php
/**
 * Debug Session and Company ID Issues
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h2>🔍 Session & Company ID Debug</h2>";

// Check session variables
echo "<h3>📋 Current Session Variables:</h3>";
echo "<pre>";
foreach ($_SESSION as $key => $value) {
    echo "$key: " . (is_null($value) ? 'NULL' : $value) . "\n";
}
echo "</pre>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ User not logged in. Please log in first.</p>";
    echo "<a href='landing.html'>Go to Login</a>";
    exit;
}

// Check database connection
try {
    $db = $database->getConnection();
    echo "<p style='color: green;'>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

// Check companies table
echo "<h3>🏢 Companies in Database:</h3>";
try {
    $stmt = $db->query("SELECT * FROM companies ORDER BY id");
    $companies = $stmt->fetchAll();
    
    if (empty($companies)) {
        echo "<p style='color: orange;'>⚠️ No companies found in database</p>";
        
        // Create a default company
        echo "<p>Creating default company...</p>";
        $stmt = $db->prepare("INSERT INTO companies (name, created_at) VALUES (?, NOW())");
        $stmt->execute(['Default Company']);
        $companyId = $db->lastInsertId();
        echo "<p style='color: green;'>✅ Created default company with ID: $companyId</p>";
        
        // Update session
        $_SESSION['company_id'] = $companyId;
        echo "<p style='color: green;'>✅ Updated session company_id to: $companyId</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Created</th></tr>";
        foreach ($companies as $company) {
            echo "<tr>";
            echo "<td>" . $company['id'] . "</td>";
            echo "<td>" . htmlspecialchars($company['name']) . "</td>";
            echo "<td>" . $company['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking companies: " . $e->getMessage() . "</p>";
}

// Check current user details
echo "<h3>👤 Current User Details:</h3>";
try {
    $stmt = $db->prepare("SELECT u.*, e.company_id as employee_company_id FROM users u LEFT JOIN employees e ON u.employee_id = e.id WHERE u.id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        foreach ($user as $key => $value) {
            echo "<tr>";
            echo "<td>$key</td>";
            echo "<td>" . (is_null($value) ? 'NULL' : htmlspecialchars($value)) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Fix company_id if needed
        if (!$user['company_id'] && !empty($companies)) {
            $defaultCompanyId = $companies[0]['id'];
            echo "<p style='color: orange;'>⚠️ User has no company_id, setting to default: $defaultCompanyId</p>";
            
            $stmt = $db->prepare("UPDATE users SET company_id = ? WHERE id = ?");
            $stmt->execute([$defaultCompanyId, $_SESSION['user_id']]);
            
            $_SESSION['company_id'] = $defaultCompanyId;
            echo "<p style='color: green;'>✅ Updated user and session company_id</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ User not found in database</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking user: " . $e->getMessage() . "</p>";
}

// Final session check
echo "<h3>🎯 Final Session State:</h3>";
echo "<pre>";
foreach ($_SESSION as $key => $value) {
    echo "$key: " . (is_null($value) ? 'NULL' : $value) . "\n";
}
echo "</pre>";

if (isset($_SESSION['company_id']) && $_SESSION['company_id'] !== null) {
    echo "<p style='color: green; font-weight: bold;'>✅ Company ID is now set: " . $_SESSION['company_id'] . "</p>";
    echo "<p><a href='index.php?page=employees&action=list'>Go to Employee Management</a></p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ Company ID still not set</p>";
}

echo "<hr>";
echo "<p><a href='index.php?page=dashboard'>Back to Dashboard</a></p>";
?>
