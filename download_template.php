<?php
/**
 * CSV/Excel Template Download for Employee Bulk Import
 */

session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

/**
 * Generate Excel template file
 */
function generateExcelTemplate() {
    global $database;

    // Core CSV headers (always included)
    $headers = [
        'first_name',
        'middle_name',
        'last_name',
        'id_number',
        'email',
        'phone',
        'hire_date',
        'basic_salary',
        'department_name',
        'position_title',
        'employment_type'
    ];

    // Optional headers (only include if columns exist in database)
    $optionalHeaders = [
        'kra_pin',
        'nssf_number',
        'shif_id',
        'bank_code',
        'bank_name',
        'bank_branch',
        'account_number'
    ];

    // Check which optional columns exist
    foreach ($optionalHeaders as $column) {
        try {
            if (isset($database) && $database) {
                $database->query("SELECT $column FROM employees LIMIT 1");
                $headers[] = $column;
            }
        } catch (Exception $e) {
            // Column doesn't exist, don't include it
        }
    }

    // Sample data with proper string formatting (preserving leading zeros)
    $sampleData = [
        [
            'first_name' => 'John',
            'middle_name' => 'Doe',
            'last_name' => 'Smith',
            'id_number' => '********',
            'email' => '<EMAIL>',
            'phone' => '+************',
            'hire_date' => '2024-01-15',
            'basic_salary' => '50,000.00',
            'department_name' => 'Information Technology',
            'position_title' => 'Software Developer',
            'employment_type' => 'permanent',
            'kra_pin' => 'A********9B',
            'nssf_number' => '000123',
            'shif_id' => '000456',
            'bank_code' => '01100',
            'bank_name' => 'Kenya Commercial Bank Limited',
            'bank_branch' => 'Moi Avenue Nairobi',
            'account_number' => '**********'
        ],
        [
            'first_name' => 'Jane',
            'middle_name' => 'Mary',
            'last_name' => 'Doe',
            'id_number' => '********',
            'email' => '<EMAIL>',
            'phone' => '+************',
            'hire_date' => '2024-02-01',
            'basic_salary' => '75,000',
            'department_name' => 'Human Resources',
            'position_title' => 'HR Manager',
            'employment_type' => 'permanent',
            'kra_pin' => 'A********7C',
            'nssf_number' => '001234',
            'shif_id' => '001789',
            'bank_code' => '17002',
            'bank_name' => 'Equity Bank Kenya Limited',
            'bank_branch' => 'Kenyatta Avenue',
            'account_number' => '00********'
        ],
        [
            'first_name' => 'Peter',
            'middle_name' => 'Kamau',
            'last_name' => 'Mwangi',
            'id_number' => '********',
            'email' => '<EMAIL>',
            'phone' => '**********',
            'hire_date' => '2024-03-01',
            'basic_salary' => '7,000',
            'department_name' => 'Finance',
            'position_title' => 'Accountant',
            'employment_type' => 'contract',
            'kra_pin' => 'A000987654D',
            'nssf_number' => '000789',
            'shif_id' => '000012',
            'bank_code' => '11003',
            'bank_name' => 'Co-operative Bank of Kenya Limited',
            'bank_branch' => 'Kisumu',
            'account_number' => '**********'
        ]
    ];

    // Generate CSV content and convert to simple Excel format
    $csvContent = '';

    // Add headers
    $csvContent .= implode(',', array_map(function($h) {
        return '"' . str_replace('"', '""', $h) . '"';
    }, $headers)) . "\n";

    // Add sample data
    foreach ($sampleData as $row) {
        $rowData = [];
        foreach ($headers as $header) {
            $rowData[] = '"' . str_replace('"', '""', $row[$header] ?? '') . '"';
        }
        $csvContent .= implode(',', $rowData) . "\n";
    }

    // For simplicity, we'll output CSV content with Excel headers
    // In a production environment, consider using PhpSpreadsheet for proper Excel generation
    echo $csvContent;
}

// Check if user is logged in and has permission
if (!isset($_SESSION['user_id']) || !hasPermission('hr')) {
    http_response_code(403);
    exit('Access denied');
}

$type = $_GET['type'] ?? 'employees';
$format = $_GET['format'] ?? 'csv';

if ($type === 'employees') {
    if ($format === 'excel') {
        // Set headers for Excel download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="employee_import_template.xlsx"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        // Generate Excel file content (basic XML structure)
        generateExcelTemplate();
        exit;
    } else {
        // Set headers for CSV download
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="employee_import_template.csv"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        // Create file pointer connected to the output stream
        $output = fopen('php://output', 'w');
    }

    // Core CSV headers (always included)
    $headers = [
        'first_name',
        'middle_name',
        'last_name',
        'id_number',
        'email',
        'phone',
        'hire_date',
        'basic_salary',
        'department_name',
        'position_title',
        'employment_type'
    ];

    // Optional headers (only include if columns exist in database)
    $optionalHeaders = [
        'kra_pin',
        'nssf_number',
        'shif_id',
        'bank_code',
        'bank_name',
        'bank_branch',
        'account_number'
    ];

    // Check which optional columns exist
    foreach ($optionalHeaders as $column) {
        try {
            if (isset($database) && $database) {
                $database->query("SELECT $column FROM employees LIMIT 1");
                $headers[] = $column;
            }
        } catch (Exception $e) {
            // Column doesn't exist, don't include it
        }
    }

    // Write headers
    fputcsv($output, $headers);

    // Sample data rows - build dynamically based on available headers
    $baseSampleData = [
        [
            'first_name' => 'John',
            'middle_name' => 'Doe',
            'last_name' => 'Smith',
            'id_number' => '********',
            'email' => '<EMAIL>',
            'phone' => '+************',
            'hire_date' => '2024-01-15',
            'basic_salary' => '50,000.00',
            'department_name' => 'Information Technology',
            'position_title' => 'Software Developer',
            'employment_type' => 'permanent',
            'kra_pin' => 'A********9B',
            'nssf_number' => '000123',
            'shif_id' => '000456',
            'bank_code' => '01',
            'bank_name' => 'Equity Bank',
            'bank_branch' => 'Nairobi Branch',
            'account_number' => '**********'
        ],
        [
            'first_name' => 'Jane',
            'middle_name' => 'Mary',
            'last_name' => 'Doe',
            'id_number' => '********',
            'email' => '<EMAIL>',
            'phone' => '+************',
            'hire_date' => '2024-02-01',
            'basic_salary' => '75,000',
            'department_name' => 'Human Resources',
            'position_title' => 'HR Manager',
            'employment_type' => 'permanent',
            'kra_pin' => 'B9********C',
            'nssf_number' => '001234',
            'shif_id' => '001789',
            'bank_code' => '02',
            'bank_name' => 'Kenya Commercial Bank (KCB)',
            'bank_branch' => 'Westlands Branch',
            'account_number' => '00********'
        ],
        [
            'first_name' => 'Peter',
            'middle_name' => '',
            'last_name' => 'Kamau',
            'id_number' => '********',
            'email' => '<EMAIL>',
            'phone' => '+************',
            'hire_date' => '2024-03-01',
            'basic_salary' => '35000.00',
            'department_name' => 'Finance',
            'position_title' => 'Accountant',
            'employment_type' => 'contract',
            'kra_pin' => 'C********5D',
            'nssf_number' => '345678',
            'shif_id' => '876543',
            'bank_code' => '12',
            'bank_name' => 'Cooperative Bank of Kenya',
            'bank_branch' => 'CBD Branch',
            'account_number' => '**********'
        ]
    ];

    // Build sample data rows based on available headers
    $sampleData = [];
    foreach ($baseSampleData as $baseRow) {
        $row = [];
        foreach ($headers as $header) {
            $row[] = $baseRow[$header] ?? '';
        }
        $sampleData[] = $row;
    }

    // Write sample data with proper string formatting for bank codes
    foreach ($sampleData as $row) {
        // Ensure bank_code is treated as string to preserve leading zeros
        if (isset($row['bank_code'])) {
            $row['bank_code'] = strval($row['bank_code']);
        }
        fputcsv($output, $row);
    }

    // Close the file pointer
    fclose($output);
    exit;
}

// If invalid type, return error
http_response_code(400);
exit('Invalid template type');
?>
