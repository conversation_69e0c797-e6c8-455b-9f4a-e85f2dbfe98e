<?php
/**
 * Fix Contract Type Default Value
 * 
 * This script updates the contract_type column default from 'permanent' to 'contract'
 * in the employees table to match the new business requirement.
 */

session_start();
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('❌ Database connection failed.');
}

echo "<h2>🔧 Fixing Contract Type Default Value</h2>";

try {
    // Step 1: Check current structure
    echo "<h3>📋 Step 1: Current Database Structure</h3>";
    
    $stmt = $db->query("DESCRIBE employees");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $contractTypeColumn = null;
    foreach ($columns as $column) {
        if ($column['Field'] === 'contract_type') {
            $contractTypeColumn = $column;
            break;
        }
    }
    
    if ($contractTypeColumn) {
        echo "<p><strong>Current contract_type column:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        echo "<tr>";
        echo "<td>{$contractTypeColumn['Field']}</td>";
        echo "<td>{$contractTypeColumn['Type']}</td>";
        echo "<td>{$contractTypeColumn['Null']}</td>";
        echo "<td>{$contractTypeColumn['Key']}</td>";
        echo "<td style='background: #fff3cd;'><strong>{$contractTypeColumn['Default']}</strong></td>";
        echo "<td>{$contractTypeColumn['Extra']}</td>";
        echo "</tr>";
        echo "</table>";
        
        if ($contractTypeColumn['Default'] === 'permanent') {
            echo "<p style='color: orange;'>⚠️ Default is currently 'permanent' - needs to be changed to 'contract'</p>";
        } else {
            echo "<p style='color: green;'>✅ Default is already '{$contractTypeColumn['Default']}'</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ contract_type column not found!</p>";
        exit;
    }
    
    // Step 2: Update the column default
    echo "<h3>🔄 Step 2: Updating Column Default</h3>";
    
    if ($contractTypeColumn['Default'] === 'permanent') {
        // Check if the column is ENUM and what values it has
        $enumValues = $contractTypeColumn['Type'];
        echo "<p><strong>Current ENUM values:</strong> $enumValues</p>";
        
        // Update the column to change default from 'permanent' to 'contract'
        $alterSQL = "ALTER TABLE employees MODIFY COLUMN contract_type ENUM('permanent', 'contract', 'temporary', 'intern', 'casual', 'consultant', 'probation') DEFAULT 'contract'";
        
        echo "<p><strong>Executing:</strong> <code>$alterSQL</code></p>";
        
        $db->exec($alterSQL);
        echo "<p style='color: green;'>✅ Column default updated successfully!</p>";
        
        // Verify the change
        $stmt = $db->query("DESCRIBE employees");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'contract_type') {
                echo "<p><strong>New contract_type column:</strong></p>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>Field</th><th>Type</th><th>Default</th></tr>";
                echo "<tr>";
                echo "<td>{$column['Field']}</td>";
                echo "<td>{$column['Type']}</td>";
                echo "<td style='background: #d4edda;'><strong>{$column['Default']}</strong></td>";
                echo "</tr>";
                echo "</table>";
                break;
            }
        }
    } else {
        echo "<p style='color: green;'>✅ Default is already correct - no changes needed</p>";
    }
    
    // Step 3: Check existing employee records
    echo "<h3>📊 Step 3: Existing Employee Records Analysis</h3>";
    
    $stmt = $db->query("
        SELECT 
            contract_type,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM employees), 2) as percentage
        FROM employees 
        GROUP BY contract_type 
        ORDER BY count DESC
    ");
    $contractStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($contractStats)) {
        echo "<p><strong>Current employee contract types:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Contract Type</th><th>Count</th><th>Percentage</th></tr>";
        
        foreach ($contractStats as $stat) {
            $type = $stat['contract_type'] ?: '<em>NULL</em>';
            echo "<tr>";
            echo "<td><strong>$type</strong></td>";
            echo "<td>{$stat['count']}</td>";
            echo "<td>{$stat['percentage']}%</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No employees found in database.</p>";
    }
    
    // Step 4: Test new employee creation
    echo "<h3>🧪 Step 4: Testing New Employee Default</h3>";
    
    // Create a test to see what default value would be used
    echo "<p>Testing what default value would be used for new employees...</p>";
    
    try {
        // Start a transaction for testing
        $db->beginTransaction();
        
        // Insert a test employee without specifying contract_type
        $testSQL = "
            INSERT INTO employees (
                company_id, employee_number, first_name, last_name, email, hire_date
            ) VALUES (
                1, 'TEST_DEFAULT', 'Test', 'Employee', '<EMAIL>', CURDATE()
            )
        ";
        
        $db->exec($testSQL);
        
        // Check what default was used
        $stmt = $db->query("SELECT contract_type FROM employees WHERE employee_number = 'TEST_DEFAULT'");
        $defaultUsed = $stmt->fetchColumn();
        
        // Rollback the test
        $db->rollback();
        
        echo "<p><strong>Test Result:</strong> New employees would get contract_type = '<strong>$defaultUsed</strong>'</p>";
        
        if ($defaultUsed === 'contract') {
            echo "<p style='color: green;'>✅ Perfect! New employees will default to 'contract'</p>";
        } else {
            echo "<p style='color: red;'>❌ Issue: New employees are defaulting to '$defaultUsed' instead of 'contract'</p>";
        }
        
    } catch (Exception $e) {
        $db->rollback();
        echo "<p style='color: orange;'>⚠️ Test failed (this is normal if no companies exist): " . $e->getMessage() . "</p>";
    }
    
    // Step 5: Update the schema file for future installations
    echo "<h3>📝 Step 5: Schema File Update Recommendation</h3>";
    
    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px;'>";
    echo "<h4>📋 Schema File Update Needed</h4>";
    echo "<p>To ensure future installations use the correct default, update the schema file:</p>";
    echo "<p><strong>File:</strong> <code>complete_database_schema.sql</code></p>";
    echo "<p><strong>Change line 135 from:</strong></p>";
    echo "<code>contract_type ENUM('permanent', 'contract', 'temporary', 'intern') DEFAULT 'permanent',</code>";
    echo "<p><strong>To:</strong></p>";
    echo "<code>contract_type ENUM('permanent', 'contract', 'temporary', 'intern', 'casual', 'consultant', 'probation') DEFAULT 'contract',</code>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🎉 Contract Type Default Fix Complete!</h4>";
    echo "<p><strong>What's been fixed:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Database Default:</strong> contract_type column now defaults to 'contract'</li>";
    echo "<li>✅ <strong>ENUM Values:</strong> Added more employment types (casual, consultant, probation)</li>";
    echo "<li>✅ <strong>New Employees:</strong> Will automatically get 'contract' as employment type</li>";
    echo "<li>✅ <strong>Bulk Import:</strong> Will use 'contract' as default when employment_type is empty</li>";
    echo "</ul>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Test creating a new employee to verify the default</li>";
    echo "<li>Test bulk import to ensure it uses 'contract' as default</li>";
    echo "<li>Update the schema file for future installations</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ Fix Failed</h4>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='index.php?page=employees&action=add' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>👤 Test Add Employee</a>";
echo "<a href='index.php?page=employees&action=bulk_import' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>📤 Test Bulk Import</a>";
echo "</div>";
?>
