<?php
/**
 * Database Backup Script
 * Creates a complete backup before running migrations
 */

require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('❌ Database connection failed.');
}

$backupDir = 'backups';
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
}

$timestamp = date('Y-m-d_H-i-s');
$backupFile = $backupDir . '/backup_before_migration_' . $timestamp . '.sql';

echo "<h2>🔄 Creating Database Backup</h2>";
echo "<p>Backup file: $backupFile</p>";

try {
    // Get all tables
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $backup = "-- Database Backup Created: " . date('Y-m-d H:i:s') . "\n";
    $backup .= "-- Before Migration to Version 2.0 (SHIF Compliant)\n\n";
    
    foreach ($tables as $table) {
        // Get table structure
        $stmt = $db->query("SHOW CREATE TABLE `$table`");
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $backup .= "-- Table: $table\n";
        $backup .= "DROP TABLE IF EXISTS `$table`;\n";
        $backup .= $row['Create Table'] . ";\n\n";
        
        // Get table data
        $stmt = $db->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $backup .= "-- Data for table: $table\n";
            foreach ($rows as $row) {
                $values = array_map(function($value) use ($db) {
                    return $value === null ? 'NULL' : $db->quote($value);
                }, array_values($row));
                $backup .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
            }
            $backup .= "\n";
        }
    }
    
    file_put_contents($backupFile, $backup);
    echo "<p style='color: green;'>✅ Backup created successfully!</p>";
    echo "<p>You can now safely run the migration.</p>";
    echo "<a href='master_migration_v2.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Start Migration</a>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Backup failed: " . $e->getMessage() . "</p>";
}
?>
