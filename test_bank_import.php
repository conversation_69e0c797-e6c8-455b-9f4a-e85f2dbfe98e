<?php
/**
 * Test Bank Code Import Functionality
 * 
 * This script tests the automatic bank name/branch population
 * during bulk employee imports when only bank codes are provided.
 */

// Include the bank code loading function
require_once 'pages/employees.php';

echo "<h2>🏦 Testing Bank Code Import Functionality</h2>";

// Test the bank codes loading
echo "<h3>📋 Step 1: Loading Bank Codes Data</h3>";

// Load bank codes (this function is now in employees.php)
function loadBankCodesData() {
    $bankCodes = [];
    $filePath = 'bank codes.txt';
    
    if (!file_exists($filePath)) {
        echo "<p style='color: orange;'>⚠️ Bank codes file not found, using fallback data</p>";
        return [
            '01094' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Head Office'],
            '01100' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Moi Avenue Nairobi'],
            '02008' => ['bankName' => 'Standard Chartered Bank Kenya Ltd', 'branchName' => 'Moi Avenue'],
            '03001' => ['bankName' => 'Absa Bank Kenya Plc', 'branchName' => 'Head Office - Vpc'],
            '11000' => ['bankName' => 'Co-operative Bank of Kenya Limited', 'branchName' => 'Head Office'],
            '12000' => ['bankName' => 'National Bank Of Kenya', 'branchName' => 'Central Business Unit']
        ];
    }
    
    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if ($lines === false) {
        echo "<p style='color: red;'>❌ Failed to read bank codes file</p>";
        return [];
    }
    
    echo "<p style='color: green;'>✅ Bank codes file found with " . count($lines) . " lines</p>";
    
    // Skip header line
    array_shift($lines);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Parse the line: Bank Name Bank Code Branch Code Branch Name
        $parts = preg_split('/\s+/', $line, 4);
        
        if (count($parts) >= 4) {
            $bankName = $parts[0];
            $bankCode = $parts[1];
            $branchCode = $parts[2];
            $branchName = $parts[3];
            
            // Handle multi-word bank names
            if (count($parts) > 4) {
                for ($i = 1; $i < count($parts) - 2; $i++) {
                    if (preg_match('/^\d{2}$/', $parts[$i])) {
                        $bankCode = $parts[$i];
                        $branchCode = $parts[$i + 1];
                        $bankName = implode(' ', array_slice($parts, 0, $i));
                        $branchName = implode(' ', array_slice($parts, $i + 2));
                        break;
                    }
                }
            }
            
            // Ensure bank code has leading zero if needed
            $bankCodeStr = str_pad(strval($bankCode), 2, '0', STR_PAD_LEFT);
            $branchCodeStr = str_pad(strval($branchCode), 3, '0', STR_PAD_LEFT);
            $fullCode = $bankCodeStr . $branchCodeStr;
            
            // Validate codes
            if (preg_match('/^\d{2}$/', $bankCodeStr) && preg_match('/^\d{3}$/', $branchCodeStr)) {
                $bankCodes[$fullCode] = [
                    'bankName' => trim($bankName),
                    'branchName' => trim($branchName)
                ];
            }
        }
    }
    
    return $bankCodes;
}

$bankCodesData = loadBankCodesData();
echo "<p style='color: green;'>✅ Loaded " . count($bankCodesData) . " bank codes</p>";

// Test bank code lookup
echo "<h3>🔍 Step 2: Testing Bank Code Lookup</h3>";

$testCodes = ['01094', '01100', '02008', '03001', '11000', '12000'];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'><th>Bank Code</th><th>Bank Name</th><th>Branch Name</th><th>Status</th></tr>";

foreach ($testCodes as $code) {
    if (isset($bankCodesData[$code])) {
        $bankData = $bankCodesData[$code];
        echo "<tr>";
        echo "<td><strong>$code</strong></td>";
        echo "<td>{$bankData['bankName']}</td>";
        echo "<td>{$bankData['branchName']}</td>";
        echo "<td style='color: green;'>✅ Found</td>";
        echo "</tr>";
    } else {
        echo "<tr>";
        echo "<td><strong>$code</strong></td>";
        echo "<td colspan='2'>-</td>";
        echo "<td style='color: red;'>❌ Not Found</td>";
        echo "</tr>";
    }
}
echo "</table>";

// Test the import simulation
echo "<h3>🧪 Step 3: Simulating Import Process</h3>";

$testEmployees = [
    [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'bank_code' => '01094',
        'bank_name' => '',  // Empty - should be auto-populated
        'bank_branch' => '', // Empty - should be auto-populated
        'bank_account' => '**********'
    ],
    [
        'first_name' => 'Jane',
        'last_name' => 'Smith',
        'bank_code' => '02008',
        'bank_name' => 'Custom Bank Name',  // Provided - should be preserved
        'bank_branch' => 'Custom Branch',   // Provided - should be preserved
        'bank_account' => '**********'
    ],
    [
        'first_name' => 'Bob',
        'last_name' => 'Wilson',
        'bank_code' => '99999',  // Invalid code
        'bank_name' => '',
        'bank_branch' => '',
        'bank_account' => '**********'
    ]
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th>Employee</th><th>Input Bank Code</th><th>Input Bank Name</th><th>Input Branch</th>";
echo "<th>Final Bank Name</th><th>Final Branch</th><th>Result</th>";
echo "</tr>";

foreach ($testEmployees as $employee) {
    $bankCode = null;
    $bankName = null;
    $bankBranch = null;
    
    if (!empty($employee['bank_code'])) {
        $bankCode = strval(str_pad(trim($employee['bank_code']), 5, '0', STR_PAD_LEFT));
        
        // Auto-populate bank name and branch from KBA data if not provided
        if (isset($bankCodesData[$bankCode])) {
            $bankName = !empty($employee['bank_name']) ? trim($employee['bank_name']) : $bankCodesData[$bankCode]['bankName'];
            $bankBranch = !empty($employee['bank_branch']) ? trim($employee['bank_branch']) : $bankCodesData[$bankCode]['branchName'];
            $status = "✅ Auto-populated";
        } else {
            $bankName = !empty($employee['bank_name']) ? trim($employee['bank_name']) : null;
            $bankBranch = !empty($employee['bank_branch']) ? trim($employee['bank_branch']) : null;
            $status = "⚠️ Code not found";
        }
    }
    
    echo "<tr>";
    echo "<td>{$employee['first_name']} {$employee['last_name']}</td>";
    echo "<td>{$employee['bank_code']}</td>";
    echo "<td>" . ($employee['bank_name'] ?: '<em>empty</em>') . "</td>";
    echo "<td>" . ($employee['bank_branch'] ?: '<em>empty</em>') . "</td>";
    echo "<td><strong>" . ($bankName ?: '<em>null</em>') . "</strong></td>";
    echo "<td><strong>" . ($bankBranch ?: '<em>null</em>') . "</strong></td>";
    echo "<td>$status</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>📝 Summary</h3>";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
echo "<h4>✅ Bank Code Import Enhancement Complete!</h4>";
echo "<p><strong>What's Fixed:</strong></p>";
echo "<ul>";
echo "<li>🏦 <strong>Automatic Bank Name Population:</strong> When only bank code is provided in CSV, bank name is automatically filled from KBA data</li>";
echo "<li>🏢 <strong>Automatic Branch Population:</strong> Branch names are automatically populated from official KBA data</li>";
echo "<li>📝 <strong>CSV Override Support:</strong> If bank name/branch are provided in CSV, they take precedence over auto-population</li>";
echo "<li>🔍 <strong>Fallback Handling:</strong> If bank code is not found in KBA data, uses whatever is provided in CSV</li>";
echo "<li>🔢 <strong>Code Formatting:</strong> Bank codes are properly formatted with leading zeros (e.g., '1094' becomes '01094')</li>";
echo "</ul>";
echo "<p><strong>How it works:</strong></p>";
echo "<ol>";
echo "<li>During bulk import, the system loads the latest KBA bank codes data</li>";
echo "<li>For each employee, if bank_code is provided but bank_name/bank_branch are empty, they are auto-populated</li>";
echo "<li>If bank_name/bank_branch are provided in CSV, they are preserved (manual override)</li>";
echo "<li>Invalid bank codes fall back to using CSV data or null values</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='index.php?page=employees&action=bulk_import' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 Test Bulk Import</a>";
echo "</div>";
?>
