# 🏦 Kenyan Payroll Management System - Database Schema

## 📋 Overview

Complete database schema for a Kenyan payroll management system with SHIF compliance and enhanced banking integration.

## 📁 Schema Files

- **`complete_database_schema.sql`** - MySQL/MariaDB version
- **`complete_database_schema_sqlite.sql`** - SQLite version
- **`SCHEMA_README.md`** - This documentation file

## 🏗️ Database Structure

### 📊 Core Tables (10 tables)

1. **`companies`** - Company information and settings
2. **`users`** - System users with role-based access
3. **`departments`** - Organizational departments
4. **`job_positions`** - Job titles and salary ranges
5. **`payroll_periods`** - Monthly/periodic payroll cycles
6. **`employees`** - Employee records with enhanced banking
7. **`allowances`** - Employee allowances and benefits
8. **`deductions`** - Employee deductions and loans
9. **`leave_applications`** - Leave management system
10. **`payroll_records`** - Calculated payroll data

## 🇰🇪 Kenya-Specific Features

### 🏥 SHIF Compliance (Updated 2025)
- **`employees.shif_id`** - Social Health Insurance Fund ID (formerly NHIF)
- **`payroll_records.shif_deduction`** - SHIF deductions in payroll

### 🏛️ Statutory Fields
- **`kra_pin`** - Kenya Revenue Authority PIN
- **`nssf_number`** - National Social Security Fund Number
- **`shif_id`** - Social Health Insurance Fund ID

### 🏦 Enhanced Banking System
- **`bank_code`** - 5-digit bank-branch codes (e.g., '01094', '02008')
- **`bank_name`** - Full bank name (auto-populated)
- **`bank_branch`** - Branch name (auto-populated)
- **`bank_account`** - Account number (preserves leading zeros)

## 💰 Payroll Calculation Flow

```
Basic Salary + Allowances + Overtime = Gross Pay
Gross Pay - (PAYE + NSSF + SHIF + Housing Levy + Other Deductions) = Net Pay
```

### 📊 Statutory Deductions (Kenya)
- **PAYE Tax** - Pay As You Earn income tax
- **NSSF** - National Social Security Fund (2% of gross)
- **SHIF** - Social Health Insurance Fund (2.75% of gross)
- **Housing Levy** - Affordable Housing Levy (1.5% of gross)

## 🔧 Technical Features

### 🚀 Performance
- **Comprehensive indexes** on all frequently queried fields
- **Foreign key constraints** for data integrity
- **Unique constraints** for business rules
- **Optimized queries** for payroll calculations

### 🔒 Security
- **Password hashing** with bcrypt
- **Role-based access control** (admin, hr, employee)
- **Company-level data isolation**
- **Audit trails** with timestamps

### 🌐 Multi-Company Support
- **Company-scoped data** - all records linked to company_id
- **Isolated environments** - companies cannot see each other's data
- **Scalable architecture** - supports unlimited companies

## 📋 Sample Data Included

### 🏢 Default Company
- **Sample Company Ltd** with Kenyan address and details
- **Admin user** (username: admin, password: admin123)

### 🏗️ Organizational Structure
- **5 departments** (HR, IT, Finance, Operations, Sales)
- **5 job positions** with salary ranges
- **Sample payroll period** (July 2025)

## 🔄 Database Differences

### MySQL/MariaDB Features
- **AUTO_INCREMENT** for primary keys
- **ENUM types** for constrained values
- **InnoDB engine** with UTF-8 encoding
- **ON UPDATE CURRENT_TIMESTAMP** for automatic updates

### SQLite Features
- **AUTOINCREMENT** for primary keys
- **CHECK constraints** instead of ENUM
- **PRAGMA foreign_keys** for constraint enforcement
- **Simplified data types**

## 🚀 Getting Started

### 1. Choose Your Database
- **MySQL/MariaDB**: Use `complete_database_schema.sql`
- **SQLite**: Use `complete_database_schema_sqlite.sql`

### 2. Create Database
```sql
-- MySQL
CREATE DATABASE kenyan_payroll CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE kenyan_payroll;
SOURCE complete_database_schema.sql;

-- SQLite
sqlite3 kenyan_payroll.db < complete_database_schema_sqlite.sql
```

### 3. Default Login
- **Username**: admin
- **Password**: admin123
- **⚠️ Change password in production!**

## 📊 Key Relationships

```
companies (1) → (many) users
companies (1) → (many) departments
companies (1) → (many) employees
companies (1) → (many) payroll_periods

departments (1) → (many) job_positions
departments (1) → (many) employees

employees (1) → (many) allowances
employees (1) → (many) deductions
employees (1) → (many) leave_applications
employees (1) → (many) payroll_records

payroll_periods (1) → (many) payroll_records
```

## 🔧 Maintenance

### 📈 Regular Tasks
- **Backup database** before payroll processing
- **Archive old payroll periods** annually
- **Update statutory rates** as per Kenya law changes
- **Review user access** quarterly

### 🔄 Schema Updates
- **Migration scripts** available for safe updates
- **Backward compatibility** maintained where possible
- **Test migrations** on staging environment first

## 📞 Support

For schema-related questions or customizations:
- Review the inline SQL comments
- Check the schema documentation sections
- Test changes on a copy of the database first

---

**Last Updated**: July 2025  
**Version**: 2.0 (SHIF Compliant)  
**Compatibility**: MySQL 5.7+, MariaDB 10.3+, SQLite 3.25+
