<?php
/**
 * Debug Bank Import Process
 * 
 * This script helps debug why bank_name and bank_branch are not being populated
 * during bulk imports even when bank codes are provided.
 */

session_start();
require_once 'config/database.php';

$database = new Database();
$db = $database->getConnection();

if (!$db) {
    die('❌ Database connection failed.');
}

echo "<h2>🔍 Debugging Bank Import Process</h2>";

// Step 1: Check database structure
echo "<h3>📋 Step 1: Database Structure Check</h3>";

try {
    $stmt = $db->query("DESCRIBE employees");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Employees table columns:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $bankColumns = [];
    foreach ($columns as $column) {
        $isBankColumn = in_array($column['Field'], ['bank_code', 'bank_name', 'bank_branch', 'bank_account']);
        $style = $isBankColumn ? "background-color: #ffffcc;" : "";
        
        echo "<tr style='$style'>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
        
        if ($isBankColumn) {
            $bankColumns[] = $column['Field'];
        }
    }
    echo "</table>";
    
    echo "<p style='color: green;'>✅ Bank columns found: " . implode(', ', $bankColumns) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking database structure: " . $e->getMessage() . "</p>";
}

// Step 2: Check bank codes file
echo "<h3>🏦 Step 2: Bank Codes File Check</h3>";

$bankCodesFile = 'bank codes.txt';
if (file_exists($bankCodesFile)) {
    $lines = file($bankCodesFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    echo "<p style='color: green;'>✅ Bank codes file found with " . count($lines) . " lines</p>";
    
    // Show first few lines
    echo "<p><strong>First 5 lines:</strong></p>";
    echo "<pre>";
    for ($i = 0; $i < min(5, count($lines)); $i++) {
        echo htmlspecialchars($lines[$i]) . "\n";
    }
    echo "</pre>";
} else {
    echo "<p style='color: red;'>❌ Bank codes file not found at: $bankCodesFile</p>";
}

// Step 3: Test bank code loading function
echo "<h3>🔧 Step 3: Testing Bank Code Loading Function</h3>";

function loadBankCodesData() {
    $bankCodes = [];
    $filePath = 'bank codes.txt';
    
    if (!file_exists($filePath)) {
        return [
            '01094' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Head Office'],
            '01100' => ['bankName' => 'Kenya Commercial Bank Limited', 'branchName' => 'Moi Avenue Nairobi'],
            '02008' => ['bankName' => 'Standard Chartered Bank Kenya Ltd', 'branchName' => 'Moi Avenue']
        ];
    }
    
    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if ($lines === false) {
        return [];
    }
    
    // Skip header line
    array_shift($lines);
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Parse the line: Bank Name Bank Code Branch Code Branch Name
        $parts = preg_split('/\s+/', $line, 4);
        
        if (count($parts) >= 4) {
            $bankName = $parts[0];
            $bankCode = $parts[1];
            $branchCode = $parts[2];
            $branchName = $parts[3];
            
            // Handle multi-word bank names
            if (count($parts) > 4) {
                for ($i = 1; $i < count($parts) - 2; $i++) {
                    if (preg_match('/^\d{2}$/', $parts[$i])) {
                        $bankCode = $parts[$i];
                        $branchCode = $parts[$i + 1];
                        $bankName = implode(' ', array_slice($parts, 0, $i));
                        $branchName = implode(' ', array_slice($parts, $i + 2));
                        break;
                    }
                }
            }
            
            // Ensure bank code has leading zero if needed
            $bankCodeStr = str_pad(strval($bankCode), 2, '0', STR_PAD_LEFT);
            $branchCodeStr = str_pad(strval($branchCode), 3, '0', STR_PAD_LEFT);
            $fullCode = $bankCodeStr . $branchCodeStr;
            
            // Validate codes
            if (preg_match('/^\d{2}$/', $bankCodeStr) && preg_match('/^\d{3}$/', $branchCodeStr)) {
                $bankCodes[$fullCode] = [
                    'bankName' => trim($bankName),
                    'branchName' => trim($branchName)
                ];
            }
        }
    }
    
    return $bankCodes;
}

$bankCodesData = loadBankCodesData();
echo "<p style='color: green;'>✅ Loaded " . count($bankCodesData) . " bank codes</p>";

// Show sample bank codes
echo "<p><strong>Sample bank codes:</strong></p>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Code</th><th>Bank Name</th><th>Branch Name</th></tr>";
$count = 0;
foreach ($bankCodesData as $code => $data) {
    if ($count >= 5) break;
    echo "<tr>";
    echo "<td><strong>$code</strong></td>";
    echo "<td>{$data['bankName']}</td>";
    echo "<td>{$data['branchName']}</td>";
    echo "</tr>";
    $count++;
}
echo "</table>";

// Step 4: Test the import logic simulation
echo "<h3>🧪 Step 4: Simulating Import Logic</h3>";

$testEmployee = [
    'first_name' => 'Test',
    'last_name' => 'Employee',
    'bank_code' => '01094',
    'bank_name' => '',
    'bank_branch' => '',
    'bank_account' => '**********'
];

echo "<p><strong>Test Employee Data:</strong></p>";
echo "<pre>" . print_r($testEmployee, true) . "</pre>";

// Simulate the bank processing logic
$bankCode = null;
$bankName = null;
$bankBranch = null;

if (!empty($testEmployee['bank_code'])) {
    $bankCode = strval(str_pad(trim($testEmployee['bank_code']), 5, '0', STR_PAD_LEFT));
    echo "<p><strong>Formatted bank code:</strong> $bankCode</p>";
    
    // Auto-populate bank name and branch from KBA data if not provided in CSV
    if (isset($bankCodesData[$bankCode])) {
        $bankName = !empty($testEmployee['bank_name']) ? trim($testEmployee['bank_name']) : $bankCodesData[$bankCode]['bankName'];
        $bankBranch = !empty($testEmployee['bank_branch']) ? trim($testEmployee['bank_branch']) : $bankCodesData[$bankCode]['branchName'];
        echo "<p style='color: green;'>✅ Bank data found in KBA:</p>";
        echo "<ul>";
        echo "<li><strong>Bank Name:</strong> $bankName</li>";
        echo "<li><strong>Branch Name:</strong> $bankBranch</li>";
        echo "</ul>";
    } else {
        $bankName = !empty($testEmployee['bank_name']) ? trim($testEmployee['bank_name']) : null;
        $bankBranch = !empty($testEmployee['bank_branch']) ? trim($testEmployee['bank_branch']) : null;
        echo "<p style='color: red;'>❌ Bank code $bankCode not found in KBA data</p>";
    }
}

// Step 5: Check actual database records
echo "<h3>📊 Step 5: Current Database Records</h3>";

try {
    $stmt = $db->query("SELECT employee_number, first_name, last_name, bank_code, bank_name, bank_branch, bank_account FROM employees ORDER BY id DESC LIMIT 5");
    $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($employees)) {
        echo "<p>No employees found in database.</p>";
    } else {
        echo "<p><strong>Last 5 employees in database:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Employee #</th><th>Name</th><th>Bank Code</th><th>Bank Name</th><th>Branch</th><th>Account</th></tr>";
        
        foreach ($employees as $emp) {
            $bankNameStatus = $emp['bank_name'] ? "✅" : "❌ NULL";
            $branchStatus = $emp['bank_branch'] ? "✅" : "❌ NULL";
            
            echo "<tr>";
            echo "<td>{$emp['employee_number']}</td>";
            echo "<td>{$emp['first_name']} {$emp['last_name']}</td>";
            echo "<td>{$emp['bank_code']}</td>";
            echo "<td>{$emp['bank_name']} $bankNameStatus</td>";
            echo "<td>{$emp['bank_branch']} $branchStatus</td>";
            echo "<td>{$emp['bank_account']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error querying employees: " . $e->getMessage() . "</p>";
}

echo "<h3>🎯 Diagnosis</h3>";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>";
echo "<p>Based on the above checks, the issue could be:</p>";
echo "<ol>";
echo "<li><strong>Bank codes file not loading properly</strong> - Check if the file exists and is readable</li>";
echo "<li><strong>Bank code format mismatch</strong> - CSV might have different format than expected</li>";
echo "<li><strong>Database column names</strong> - Check if bank_name/bank_branch columns exist</li>";
echo "<li><strong>Import logic not executing</strong> - The bank lookup code might not be running</li>";
echo "</ol>";
echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='test_bank_import.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Run Full Test</a>";
echo "</div>";
?>
