<?php
/**
 * Employee Management Page
 */

if (!hasPermission('hr')) {
    header('Location: index.php?page=dashboard');
    exit;
}

$action = $_GET['action'] ?? 'list';
$message = '';
$messageType = '';

// Helper function to convert empty strings to NULL
function nullIfEmpty($value) {
    return isset($value) && trim($value) !== '' ? trim($value) : null;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'delete') {
        // Handle individual employee deletion
        $employeeId = $_POST['employee_id'] ?? null;
        if ($employeeId) {
            try {
                // Check if employee has payroll records
                $stmt = $db->prepare("SELECT COUNT(*) FROM payroll_records WHERE employee_id = ?");
                $stmt->execute([$employeeId]);
                $payrollCount = $stmt->fetchColumn();

                if ($payrollCount > 0) {
                    // Don't delete, just deactivate (employee has payroll history)
                    $stmt = $db->prepare("UPDATE employees SET employment_status = 'terminated' WHERE id = ? AND company_id = ?");
                    $stmt->execute([$employeeId, $_SESSION['company_id']]);
                    $message = 'Employee deactivated (has payroll records - cannot be deleted)';
                    $messageType = 'warning';
                } else {
                    // Safe to delete completely
                    $stmt = $db->prepare("DELETE FROM employees WHERE id = ? AND company_id = ?");
                    $stmt->execute([$employeeId, $_SESSION['company_id']]);
                    $message = 'Employee deleted successfully';
                    $messageType = 'success';
                }
            } catch (Exception $e) {
                $message = 'Error deleting employee: ' . $e->getMessage();
                $messageType = 'danger';
            }
        }
    } elseif ($action === 'bulk_delete') {
        // Handle bulk employee deletion
        $employeeIds = $_POST['employee_ids'] ?? [];
        if (!empty($employeeIds)) {
            try {
                $deletedCount = 0;
                $deactivatedCount = 0;

                foreach ($employeeIds as $employeeId) {
                    // Check if employee has payroll records
                    $stmt = $db->prepare("SELECT COUNT(*) FROM payroll_records WHERE employee_id = ?");
                    $stmt->execute([$employeeId]);
                    $payrollCount = $stmt->fetchColumn();

                    if ($payrollCount > 0) {
                        // Don't delete, just deactivate
                        $stmt = $db->prepare("UPDATE employees SET employment_status = 'terminated' WHERE id = ? AND company_id = ?");
                        $stmt->execute([$employeeId, $_SESSION['company_id']]);
                        $deactivatedCount++;
                    } else {
                        // Safe to delete
                        $stmt = $db->prepare("DELETE FROM employees WHERE id = ? AND company_id = ?");
                        $stmt->execute([$employeeId, $_SESSION['company_id']]);
                        $deletedCount++;
                    }
                }

                $message = "Bulk operation completed: {$deletedCount} deleted, {$deactivatedCount} deactivated";
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error in bulk deletion: ' . $e->getMessage();
                $messageType = 'danger';
            }
        } else {
            $message = 'No employees selected for deletion';
            $messageType = 'warning';
        }
    } elseif ($action === 'bulk_import') {
        // Handle CSV/Excel bulk import
        if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
            $result = handleBulkImport($_FILES['csv_file']);
            $message = $result['message'];
            $messageType = $result['type'];
        } else {
            $message = 'Please select a valid CSV or Excel file';
            $messageType = 'danger';
        }
    } elseif ($action === 'add' || $action === 'edit') {
        $employeeId    = $_POST['employee_id'] ?? null;
        $firstName     = sanitizeInput($_POST['first_name']);
        $middleName    = nullIfEmpty(sanitizeInput($_POST['middle_name']));    // Optional
        $lastName      = sanitizeInput($_POST['last_name']);
        $idNumber      = nullIfEmpty(sanitizeInput($_POST['id_number']));      // Optional
        $email         = nullIfEmpty($_POST['email']);           // Optional
        $phone         = nullIfEmpty($_POST['phone']);           // Optional
        $hireDate      = nullIfEmpty($_POST['hire_date']);       // Optional (must be date or NULL)
        $basicSalary   = nullIfEmpty($_POST['basic_salary']);    // Optional
        $departmentId  = nullIfEmpty($_POST['department_id']);   // Optional
        $positionId    = nullIfEmpty($_POST['position_id']);     // Optional
        $contractType  = nullIfEmpty($_POST['contract_type']);   // Optional
        $bankCode      = !empty($_POST['bank_code']) ? strval(trim($_POST['bank_code'])) : null;       // Optional - keep as string
        $bankName      = nullIfEmpty($_POST['bank_name']);       // Optional
        $bankBranch    = nullIfEmpty($_POST['bank_branch']);     // Optional
        $bankAccount = nullIfEmpty($_POST['bank_account']);  // Optional

        if (empty($firstName) || empty($lastName) || empty($basicSalary)) {
            $message = 'Please fill in all required fields (First Name, Last Name, Basic Salary)';
            $messageType = 'danger';
        } else {
            if ($action === 'add') {
                // Check if ID number already exists (only if ID number is provided)
                $canProceed = true;
                if (!empty($idNumber)) {
                    $stmt = $db->prepare("SELECT id FROM employees WHERE id_number = ?");
                    $stmt->execute([$idNumber]);

                    if ($stmt->fetch()) {
                        $message = 'Employee with this ID number already exists';
                        $messageType = 'danger';
                        $canProceed = false;
                    }
                }
                $hireDate = !empty($_POST['hire_date']) ? $_POST['hire_date'] : null;

                if ($canProceed) {
                    $employeeNumber = generateEmployeeNumber($_SESSION['company_id']);

                    $stmt = $db->prepare("
                        INSERT INTO employees (
                            company_id, employee_number, first_name, middle_name, last_name,
                            id_number, email, phone, hire_date, basic_salary, department_id,
                            position_id, contract_type, bank_code, bank_name, bank_branch, bank_account
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");

                    if ($stmt->execute([
                        $_SESSION['company_id'], $employeeNumber, $firstName, $middleName, $lastName,
                        $idNumber, $email, $phone, $hireDate, $basicSalary,
                        $departmentId, $positionId, $contractType, $bankCode, $bankName, $bankBranch, $bankAccount
                    ])) {
                        $message = 'Employee added successfully';
                        $messageType = 'success';
                        logActivity('employee_add', "Added employee: $firstName $middleName $lastName");
                    } else {
                        $message = 'Failed to add employee';
                        $messageType = 'danger';
                    }
                }
            } else {
                $stmt = $db->prepare("
                    UPDATE employees SET
                        first_name = ?, middle_name = ?, last_name = ?, id_number = ?,
                        email = ?, phone = ?, hire_date = ?, basic_salary = ?,
                        department_id = ?, position_id = ?, contract_type = ?,
                        bank_code = ?, bank_name = ?, bank_branch = ?, bank_account = ?
                    WHERE id = ? AND company_id = ?
                ");

                if ($stmt->execute([
                    $firstName, $middleName, $lastName, $idNumber, $email, $phone,
                    $hireDate, $basicSalary, $departmentId, $positionId, $contractType,
                    $bankCode, $bankName, $bankBranch, $bankAccount,
                    $employeeId, $_SESSION['company_id']
                ])) {
                    $message = 'Employee updated successfully';
                    $messageType = 'success';
                    logActivity('employee_update', "Updated employee: $firstName $middleName $lastName");
                } else {
                    $message = 'Failed to update employee';
                    $messageType = 'danger';
                }
            }
        }
    }
}

// Get employees list
if ($action === 'list') {
    // Check for status filter
    $statusFilter = $_GET['status'] ?? 'active';

    if ($statusFilter === 'all') {
        $whereClause = "WHERE e.company_id = ?";
        $params = [$_SESSION['company_id']];
    } else {
        $whereClause = "WHERE e.company_id = ? AND e.employment_status = ?";
        $params = [$_SESSION['company_id'], $statusFilter];
    }

    $stmt = $db->prepare("
        SELECT e.*, d.name as department_name, p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN job_positions p ON e.position_id = p.id
        $whereClause
        ORDER BY e.first_name, e.last_name
    ");
    $stmt->execute($params);
    $employees = $stmt->fetchAll();
}

// Get employee for editing or viewing
if (($action === 'edit' || $action === 'view') && isset($_GET['id'])) {
    $stmt = $db->prepare("
        SELECT e.*, d.name as department_name, p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN job_positions p ON e.position_id = p.id
        WHERE e.id = ? AND e.company_id = ?
    ");
    $stmt->execute([$_GET['id'], $_SESSION['company_id']]);
    $employee = $stmt->fetch();

    if (!$employee) {
        header('Location: index.php?page=employees');
        exit;
    }
}

// Get departments and positions for form
if ($action === 'add' || $action === 'edit') {
    $stmt = $db->prepare("SELECT * FROM departments WHERE company_id = ? ORDER BY name");
    $stmt->execute([$_SESSION['company_id']]);
    $departments = $stmt->fetchAll();
    
    $stmt = $db->prepare("SELECT * FROM job_positions WHERE company_id = ? ORDER BY title");
    $stmt->execute([$_SESSION['company_id']]);
    $positions = $stmt->fetchAll();
}

/**
 * Convert Excel file to CSV format
 */
function convertExcelToCsv($filePath, $extension) {
    // Simple Excel to CSV conversion using PHP's built-in functions
    // This is a basic implementation - for production, consider using PhpSpreadsheet

    if ($extension === 'xlsx') {
        // For XLSX files, we'll try a simple XML parsing approach
        $zip = new ZipArchive();
        if ($zip->open($filePath) === TRUE) {
            $sharedStrings = [];
            $worksheetData = '';

            // Read shared strings
            $sharedStringsXml = $zip->getFromName('xl/sharedStrings.xml');
            if ($sharedStringsXml) {
                $xml = simplexml_load_string($sharedStringsXml);
                foreach ($xml->si as $si) {
                    $sharedStrings[] = (string)$si->t;
                }
            }

            // Read first worksheet
            $worksheetXml = $zip->getFromName('xl/worksheets/sheet1.xml');
            if ($worksheetXml) {
                $xml = simplexml_load_string($worksheetXml);
                $csvLines = [];
                $currentRow = 0;

                foreach ($xml->sheetData->row as $row) {
                    $rowData = [];
                    $rowIndex = (int)$row['r'] - 1;

                    // Fill empty rows if needed
                    while ($currentRow < $rowIndex) {
                        $csvLines[] = '';
                        $currentRow++;
                    }

                    foreach ($row->c as $cell) {
                        $value = '';
                        if (isset($cell->v)) {
                            if (isset($cell['t']) && $cell['t'] == 's') {
                                // Shared string
                                $index = (int)$cell->v;
                                $value = isset($sharedStrings[$index]) ? $sharedStrings[$index] : '';
                            } else {
                                $value = (string)$cell->v;
                            }
                        }
                        $rowData[] = $value;
                    }

                    $csvLines[] = implode(',', array_map(function($field) {
                        return '"' . str_replace('"', '""', $field) . '"';
                    }, $rowData));
                    $currentRow++;
                }

                $zip->close();
                return implode("\n", $csvLines);
            }
            $zip->close();
        }
    }

    // Fallback: return false to indicate conversion failed
    return false;
}

/**
 * Parse formatted numbers (handles commas, spaces, currency symbols)
 */
function parseFormattedNumber($value) {
    if (empty($value)) return 0;

    // Convert to string and trim
    $value = trim((string)$value);

    // Remove commas, spaces, and common currency symbols
    $value = preg_replace('/[,\s]/', '', $value);
    $value = preg_replace('/[^\d\.\-]/', '', $value);

    // Convert to float
    return floatval($value);
}

/**
 * Handle bulk CSV/Excel import of employees
 */
function handleBulkImport($file) {
    global $db;

    // Ensure company_id is available (should be set by global initialization in index.php)
    if (!isset($_SESSION['company_id']) || $_SESSION['company_id'] === null) {
        return ['message' => 'Company ID not found in session. Please refresh the page or log out and log back in.', 'type' => 'danger'];
    }

    $companyId = $_SESSION['company_id'];

    $uploadDir = 'uploads/csv/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    $fileName = 'employees_import_' . date('Y-m-d_H-i-s') . '.csv';
    $filePath = $uploadDir . $fileName;

    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        return ['message' => 'Failed to upload file', 'type' => 'danger'];
    }

    // Check if it's an Excel file and convert to CSV
    $originalFileName = $file['name'];
    $fileExtension = strtolower(pathinfo($originalFileName, PATHINFO_EXTENSION));

    if (in_array($fileExtension, ['xlsx', 'xls'])) {
        $csvContent = convertExcelToCsv($filePath, $fileExtension);
        if ($csvContent === false) {
            unlink($filePath);
            return ['message' => 'Failed to convert Excel file to CSV. Please ensure the file is not corrupted.', 'type' => 'danger'];
        }
        // Write converted CSV content to file
        file_put_contents($filePath, $csvContent);
    }

    // Read file content and detect encoding
    $content = file_get_contents($filePath);
    if ($content === false) {
        unlink($filePath);
        return ['message' => 'Failed to read uploaded file', 'type' => 'danger'];
    }

    // Remove BOM if present and normalize line endings
    $content = str_replace("\xEF\xBB\xBF", '', $content); // Remove UTF-8 BOM
    $content = str_replace(["\r\n", "\r"], "\n", $content); // Normalize line endings

    // Write cleaned content back to file
    file_put_contents($filePath, $content);

    $handle = fopen($filePath, 'r');
    if (!$handle) {
        unlink($filePath);
        return ['message' => 'Failed to read CSV file after cleaning', 'type' => 'danger'];
    }

    // Read header with improved CSV parsing
    $header = fgetcsv($handle, 0, ",", '"', "\\");

    // Validate that we actually got a header row
    if ($header === false || empty($header)) {
        fclose($handle);
        unlink($filePath);
        return ['message' => 'Invalid CSV file: Could not read header row', 'type' => 'danger'];
    }

    // Define all possible columns (both required and optional)
    $allPossibleColumns = [
        // Core required columns
        'first_name', 'last_name', 'basic_salary',
        // Core optional columns
        'middle_name', 'id_number', 'email', 'phone', 'hire_date',
        'department_name', 'position_title', 'employment_type',
        // Extended optional columns
        'kra_pin', 'nssf_number', 'shif_id', 'bank_code',
        'bank_name', 'bank_branch', 'account_number'
    ];

    // Check which columns actually exist in the database by examining table structure
    $availableDbColumns = [];
    try {
        $stmt = $db->query("DESCRIBE employees");
        while ($column = $stmt->fetch()) {
            $availableDbColumns[] = $column['Field'];
        }
    } catch (Exception $e) {
        // Fallback: assume all columns exist
        $availableDbColumns = $allPossibleColumns;
    }

    // Normalize headers (remove BOM and trim)
    $header = array_map(function($h) {
        return trim(str_replace("\xEF\xBB\xBF", '', $h));
    }, $header);

    // Additional validation: check if header looks like data instead of column names
    $suspiciousHeaderCount = 0;
    foreach ($header as $col) {
        // If header contains what looks like data (numbers, emails, etc.), it might be a data row
        if (preg_match('/^\d+$/', $col) || filter_var($col, FILTER_VALIDATE_EMAIL) || preg_match('/^\+254/', $col)) {
            $suspiciousHeaderCount++;
        }
    }

    // If more than half the headers look like data, the file might be malformed
    if ($suspiciousHeaderCount > count($header) / 2) {
        fclose($handle);
        unlink($filePath);
        return [
            'message' => 'CSV file appears to be malformed. The first row should contain column headers (first_name, last_name, etc.), not data. Please check your file format.',
            'type' => 'danger'
        ];
    }

    // Validate only required headers (core + available optional)
    $requiredHeaders = ['first_name', 'last_name', 'basic_salary']; // Only truly required fields
    $missingRequired = array_diff($requiredHeaders, $header);
    if (!empty($missingRequired)) {
        fclose($handle);
        unlink($filePath);
        return [
            'message' => 'Missing required columns: ' . implode(', ', $missingRequired) .
                        '. At minimum, you need: first_name, last_name, basic_salary. Found columns: ' . implode(', ', array_slice($header, 0, 10)) . (count($header) > 10 ? '...' : ''),
            'type' => 'danger'
        ];
    }

    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    $row = 1;

    // Get departments and positions for lookup
    $stmt = $db->prepare("SELECT id, name FROM departments WHERE company_id = ?");
    $stmt->execute([$companyId]);
    $departments = [];
    while ($dept = $stmt->fetch()) {
        $departments[strtolower($dept['name'])] = $dept['id'];
    }

    $stmt = $db->prepare("SELECT id, title FROM job_positions WHERE company_id = ?");
    $stmt->execute([$companyId]);
    $positions = [];
    while ($pos = $stmt->fetch()) {
        $positions[strtolower($pos['title'])] = $pos['id'];
    }

    while (($data = fgetcsv($handle, 0, ",", '"', "\\")) !== FALSE) {
        $row++;

        // Skip empty rows
        if (empty(array_filter($data))) {
            continue;
        }

        // Additional check: if we're getting way too many columns, the file is likely corrupted
        if (count($data) > 50) {
            $errors[] = "Row $row: Too many columns (" . count($data) . "). This suggests the CSV file is malformed.";
            $errorCount++;
            continue;
        }

        // Handle column count mismatch more gracefully
        if (count($data) !== count($header)) {
            // If we have fewer data columns, pad with empty strings
            if (count($data) < count($header)) {
                $data = array_pad($data, count($header), '');
            } else {
                // If we have more data columns, truncate to header count
                $data = array_slice($data, 0, count($header));
            }
        }

        $employee = array_combine($header, $data);

        // Clean and trim all values
        $employee = array_map(function($value) {
            return is_string($value) ? trim($value) : $value;
        }, $employee);

        // Validate required fields (only first_name, last_name, and basic_salary are required)
        if (empty($employee['first_name']) || empty($employee['last_name']) ||
            empty($employee['basic_salary'])) {
            $errors[] = "Row $row: Missing required fields (first_name, last_name, basic_salary)";
            $errorCount++;
            continue;
        }

        // Check if employee already exists (only if ID number is provided)
        if (!empty($employee['id_number'])) {
            $stmt = $db->prepare("SELECT id FROM employees WHERE id_number = ? AND company_id = ?");
            $stmt->execute([$employee['id_number'], $companyId]);
            if ($stmt->fetch()) {
                $errors[] = "Row $row: Employee with ID {$employee['id_number']} already exists";
                $errorCount++;
                continue;
            }
        }

        // Lookup department and position IDs
        $departmentId = null;
        if (!empty($employee['department_name'])) {
            $departmentId = $departments[strtolower($employee['department_name'])] ?? null;
        }

        $positionId = null;
        if (!empty($employee['position_title'])) {
            $positionId = $positions[strtolower($employee['position_title'])] ?? null;
        }

        // Validate employment type (optional, defaults to permanent)
        $validEmploymentTypes = ['permanent', 'contract', 'casual', 'intern'];
        $contractType = !empty($employee['employment_type']) ? strtolower($employee['employment_type']) : 'permanent';
        if (!in_array($contractType, $validEmploymentTypes)) {
            $contractType = 'permanent';
        }

        // Validate date format (only if hire_date is provided)
        $hireDate = null;
        if (!empty($employee['hire_date'])) {
            $hireDate = date('Y-m-d', strtotime($employee['hire_date']));
            if (!$hireDate || $hireDate === '1970-01-01') {
                $errors[] = "Row $row: Invalid hire date format";
                $errorCount++;
                continue;
            }
        }

        // Validate and parse salary (handle formatted numbers like 7,000 or 7000.00)
        $basicSalary = parseFormattedNumber($employee['basic_salary']);

        if ($basicSalary <= 0) {
            $errors[] = "Row $row: Invalid salary amount '{$employee['basic_salary']}' - must be a positive number";
            $errorCount++;
            continue;
        }

        // Validate email if provided
        if (!empty($employee['email']) && !filter_var($employee['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = "Row $row: Invalid email format";
            $errorCount++;
            continue;
        }

        // Validate and format phone number if provided
        if (!empty($employee['phone'])) {
            $phone = preg_replace('/[\s\-\(\)]/', '', $employee['phone']); // Remove spaces, dashes, parentheses
            $phone = preg_replace('/[^0-9+]/', '', $phone); // Keep only numbers and +

            // Try to format as Kenyan number
            if (preg_match('/^\+254[0-9]{9}$/', $phone)) {
                // Already in correct format
                $employee['phone'] = $phone;
            } elseif (preg_match('/^0([0-9]{9})$/', $phone, $matches)) {
                // Convert 0XXXXXXXXX to +254XXXXXXXXX
                $employee['phone'] = '+254' . $matches[1];
            } elseif (preg_match('/^254([0-9]{9})$/', $phone, $matches)) {
                // Convert 254XXXXXXXXX to +254XXXXXXXXX
                $employee['phone'] = '+254' . $matches[1];
            } elseif (preg_match('/^[0-9]{9}$/', $phone)) {
                // Convert XXXXXXXXX to +254XXXXXXXXX
                $employee['phone'] = '+254' . $phone;
            } else {
                $errors[] = "Row $row: Invalid phone number format '{$employee['phone']}' (use +254XXXXXXXXX, 0XXXXXXXXX, or XXXXXXXXX)";
                $errorCount++;
                continue;
            }
        }

        // Validate KRA PIN format if provided
        if (!empty($employee['kra_pin']) && !preg_match('/^[A-Z]\d{9}[A-Z]$/', $employee['kra_pin'])) {
            $errors[] = "Row $row: Invalid KRA PIN format (should be like P********9A)";
            $errorCount++;
            continue;
        }

        try {
            $employeeNumber = generateEmployeeNumber($companyId);

            // Check which columns exist in the employees table
            $availableColumns = [];
            $availableValues = [];

            // Prepare all possible column mappings (preserving string formats for codes/numbers)
            $allColumnMappings = [
                'company_id' => $companyId,
                'employee_number' => $employeeNumber,
                'first_name' => $employee['first_name'] ?? '',
                'middle_name' => !empty($employee['middle_name']) ? trim($employee['middle_name']) : null,
                'last_name' => $employee['last_name'] ?? '',
                'id_number' => !empty($employee['id_number']) ? trim($employee['id_number']) : null,
                'email' => !empty($employee['email']) ? trim($employee['email']) : null,
                'phone' => !empty($employee['phone']) ? $employee['phone'] : null,
                'hire_date' => $hireDate,
                'basic_salary' => $basicSalary,
                'department_id' => $departmentId,
                'position_id' => $positionId,
                'contract_type' => $contractType,
                // String fields - preserve original format including leading zeros
                'kra_pin' => !empty($employee['kra_pin']) ? trim($employee['kra_pin']) : null,
                'nssf_number' => !empty($employee['nssf_number']) ? trim($employee['nssf_number']) : null,
                'shif_id' => !empty($employee['shif_id']) ? trim($employee['shif_id']) : null,
                'bank_code' => !empty($employee['bank_code']) ? strval(str_pad(trim($employee['bank_code']), 5, '0', STR_PAD_LEFT)) : null,
                'bank_name' => !empty($employee['bank_name']) ? trim($employee['bank_name']) : null,
                'bank_branch' => !empty($employee['bank_branch']) ? trim($employee['bank_branch']) : null,
                // Handle both account_number (CSV) and bank_account (DB) field names
                'bank_account' => !empty($employee['account_number']) ? trim($employee['account_number']) : (!empty($employee['bank_account']) ? trim($employee['bank_account']) : null)
            ];

            // Only include columns that exist in the database
            $finalColumns = [];
            $finalValues = [];

            foreach ($allColumnMappings as $column => $value) {
                if (in_array($column, $availableDbColumns)) {
                    $finalColumns[] = $column;
                    $finalValues[] = $value;
                }
            }

            // Build dynamic INSERT query
            $columnList = implode(', ', $finalColumns);
            $placeholders = str_repeat('?,', count($finalColumns) - 1) . '?';

            $stmt = $db->prepare("INSERT INTO employees ($columnList) VALUES ($placeholders)");
            $stmt->execute($finalValues);

            $successCount++;

        } catch (Exception $e) {
            $errors[] = "Row $row: Database error - " . $e->getMessage();
            $errorCount++;
        }
    }

    fclose($handle);
    unlink($filePath); // Clean up uploaded file

    // Log the import activity
    logActivity('bulk_import', "Imported $successCount employees, $errorCount errors");

    $message = "Import completed: $successCount employees imported successfully";
    if ($errorCount > 0) {
        $message .= ", $errorCount errors occurred";
        if (count($errors) <= 10) {
            $message .= ":\n" . implode("\n", $errors);
        } else {
            $message .= ". First 10 errors:\n" . implode("\n", array_slice($errors, 0, 10));
        }
    }

    // Add debug information about CSV structure
    $message .= "\n\nDebug Info:";
    $message .= "\n- Required columns: " . implode(', ', ['first_name', 'last_name', 'basic_salary']);
    $message .= "\n- CSV header columns (" . count($header) . "): " . implode(', ', array_slice($header, 0, 20));
    if (count($header) > 20) {
        $message .= "... (showing first 20 of " . count($header) . " columns)";
    }
    $message .= "\n- Available DB columns: " . implode(', ', array_slice($availableDbColumns, 0, 15));
    if (count($availableDbColumns) > 15) {
        $message .= "... (showing first 15 of " . count($availableDbColumns) . " columns)";
    }
    $message .= "\n- Total rows processed: " . ($row - 1);

    // Show processing statistics
    if ($successCount > 0) {
        $message .= "\n- Successfully imported: $successCount employees";
    }
    if ($errorCount > 0) {
        $message .= "\n- Errors encountered: $errorCount rows";
    }

    return [
        'message' => $message,
        'type' => $successCount > 0 ? 'success' : 'danger'
    ];
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users"></i> Employee Management</h2>
                <?php if ($action === 'list'): ?>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="btn-group">
                            <a href="index.php?page=employees&action=add" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Employee
                            </a>
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#bulkImportModal">
                                <i class="fas fa-upload"></i> Bulk Import
                            </button>
                            <button type="button" class="btn btn-info" onclick="downloadTemplate()">
                                <i class="fas fa-download"></i> Download Template
                            </button>
                            <button type="button" class="btn btn-danger" onclick="toggleBulkDelete()" id="bulkDeleteBtn" style="display: none;">
                                <i class="fas fa-trash"></i> Bulk Delete
                            </button>
                        </div>

                        <!-- Status Filter -->
                        <div class="btn-group" role="group">
                            <a href="index.php?page=employees&status=active"
                               class="btn <?php echo ($statusFilter ?? 'active') === 'active' ? 'btn-success' : 'btn-outline-success'; ?>">
                                <i class="fas fa-user-check"></i> Active
                            </a>
                            <a href="index.php?page=employees&status=terminated"
                               class="btn <?php echo ($statusFilter ?? 'active') === 'terminated' ? 'btn-warning' : 'btn-outline-warning'; ?>">
                                <i class="fas fa-user-times"></i> Terminated
                            </a>
                            <a href="index.php?page=employees&status=all"
                               class="btn <?php echo ($statusFilter ?? 'active') === 'all' ? 'btn-secondary' : 'btn-outline-secondary'; ?>">
                                <i class="fas fa-users"></i> All
                            </a>
                        </div>
                    </div>
                <?php elseif ($action === 'bulk_import'): ?>
                    <a href="index.php?page=employees" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                <?php else: ?>
                    <a href="index.php?page=employees" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($action === 'list'): ?>
        <!-- Employee List -->
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">Employees (<?php echo count($employees); ?>)</h5>
                    </div>
                    <div class="col-auto">
                        <input type="text" class="form-control search-input" placeholder="Search employees...">
                    </div>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($employees)): ?>
                    <div class="table-responsive">
                        <form id="bulkDeleteForm" method="POST" action="index.php?page=employees&action=bulk_delete">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                        <label for="selectAll" class="ms-1">Select</label>
                                    </th>
                                    <th>Employee #</th>
                                    <th>Name</th>
                                    <th>ID Number</th>
                                    <th>Department</th>
                                    <th>Position</th>
                                    <th>Basic Salary</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($employees as $emp): ?>
                                    <tr>
                                        <td>
                                            <input type="checkbox" name="employee_ids[]" value="<?php echo $emp['id']; ?>" class="employee-checkbox" onchange="updateBulkDeleteButton()">
                                        </td>
                                        <td><?php echo htmlspecialchars($emp['employee_number']); ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="employee-avatar me-3">
                                                    <?php echo strtoupper(substr($emp['first_name'], 0, 1) . substr($emp['last_name'], 0, 1)); ?>
                                                </div>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($emp['first_name'] . ' ' . $emp['last_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($emp['email'] ?? ''); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($emp['id_number'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($emp['department_name'] ?? 'Not Assigned'); ?></td>
                                        <td><?php echo htmlspecialchars($emp['position_title'] ?? 'Not Assigned'); ?></td>
                                        <td><?php echo formatCurrency($emp['basic_salary']); ?></td>
                                        <td>
                                            <span class="badge status-<?php echo $emp['employment_status']; ?>">
                                                <?php echo ucfirst($emp['employment_status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="index.php?page=employees&action=view&id=<?php echo $emp['id']; ?>"
                                                   class="btn btn-outline-info" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="index.php?page=employees&action=edit&id=<?php echo $emp['id']; ?>"
                                                   class="btn btn-outline-primary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="index.php?page=payslips&employee_id=<?php echo $emp['id']; ?>"
                                                   class="btn btn-outline-success" title="Payslips">
                                                    <i class="fas fa-receipt"></i>
                                                </a>
                                                <button type="button" class="btn btn-outline-danger" title="Delete"
                                                        onclick="deleteEmployee(<?php echo $emp['id']; ?>, '<?php echo htmlspecialchars($emp['first_name'] . ' ' . $emp['last_name']); ?>')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        </form>

                        <!-- Bulk Delete Controls -->
                        <div id="bulkDeleteControls" style="display: none;" class="mt-3 p-3 bg-light border rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <span id="selectedCount">0 employees selected</span>
                                <div>
                                    <button type="button" class="btn btn-danger" onclick="confirmBulkDelete()">
                                        <i class="fas fa-trash"></i> Delete Selected
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearSelection()">
                                        Cancel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5>No Employees Found</h5>
                        <p class="text-muted">Start by adding your first employee to the system.</p>
                        <a href="index.php?page=employees&action=add" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Employee
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

    <?php elseif ($action === 'add' || $action === 'edit'): ?>
        <!-- Add/Edit Employee Form -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?>"></i> 
                    <?php echo $action === 'add' ? 'Add New Employee' : 'Edit Employee'; ?>
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <?php if ($action === 'edit'): ?>
                        <input type="hidden" name="employee_id" value="<?php echo $employee['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?php echo htmlspecialchars($employee['first_name'] ?? ''); ?>" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="middle_name" class="form-label">Middle Name <small class="text-muted">(optional)</small></label>
                                <input type="text" class="form-control" id="middle_name" name="middle_name" 
                                       value="<?php echo htmlspecialchars($employee['middle_name'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?php echo htmlspecialchars($employee['last_name'] ?? ''); ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_number" class="form-label">ID Number <small class="text-muted">(optional)</small></label>
                                <input type="text" class="form-control" id="id_number" name="id_number"
                                       value="<?php echo htmlspecialchars($employee['id_number'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email <small class="text-muted">(optional)</small></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($employee['email'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number <small class="text-muted">(optional)</small></label>
                                <input type="text" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($employee['phone'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hire_date" class="form-label">Hire Date <small class="text-muted">(optional)</small></label>
                                <input type="date" class="form-control" id="hire_date" name="hire_date"
                                       value="<?php echo $employee['hire_date'] ?? ''; ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="department_id" class="form-label">Department <small class="text-muted">(optional)</small></label>
                                <select class="form-select" id="department_id" name="department_id">
                                    <option value="">Select Department</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?php echo $dept['id']; ?>" 
                                                <?php echo ($employee['department_id'] ?? '') == $dept['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($dept['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="position_id" class="form-label">Position <small class="text-muted">(optional)</small></label>
                                <select class="form-select" id="position_id" name="position_id">
                                    <option value="">Select Position</option>
                                    <?php foreach ($positions as $pos): ?>
                                        <option value="<?php echo $pos['id']; ?>" 
                                                <?php echo ($employee['position_id'] ?? '') == $pos['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($pos['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="contract_type" class="form-label">Employment Type <small class="text-muted">(optional)</small></label>
                                <select class="form-select" id="contract_type" name="contract_type">
                                    <option value="">Select Type</option>
                                    <option value="permanent" <?php echo ($employee['contract_type'] ?? '') === 'permanent' ? 'selected' : ''; ?>>Permanent</option>
                                    <option value="contract" <?php echo ($employee['contract_type'] ?? '') === 'contract' ? 'selected' : ''; ?>>Contract</option>
                                    <option value="casual" <?php echo ($employee['contract_type'] ?? '') === 'casual' ? 'selected' : ''; ?>>Casual</option>
                                    <option value="intern" <?php echo ($employee['contract_type'] ?? '') === 'intern' ? 'selected' : ''; ?>>Intern</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="basic_salary" class="form-label">Basic Salary (KES) *</label>
                                <input type="number" class="form-control currency-input" id="basic_salary" name="basic_salary"
                                       value="<?php echo $employee['basic_salary'] ?? ''; ?>" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>

                    <!-- Banking Information Section -->
                    <div class="card mt-4">
                        <div class="card-header" style="background: linear-gradient(135deg, var(--kenya-green), var(--kenya-dark-green)); color: white;">
                            <h6 class="mb-0"><i class="fas fa-university"></i> Banking Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="bank_code" class="form-label">Bank Code <small class="text-muted">(optional)</small></label>
                                        <input type="text" class="form-control" id="bank_code" name="bank_code"
                                               value="<?php echo htmlspecialchars($employee['bank_code'] ?? ''); ?>"
                                               placeholder="Enter bank-branch code (e.g., 01340, 02015, 03001)"
                                               oninput="validateBankCode(this)"
                                               onblur="updateBankDetails()"
                                               autocomplete="off"
                                               list="bank_codes_list">

                                        <datalist id="bank_codes_list">
                                            <!-- Bank codes will be populated by JavaScript -->
                                        </datalist>

                                        <div class="form-text">
                                            Enter the complete bank-branch code (5 digits).
                                            <a href="#" onclick="showBankCodesModal(); return false;">View all bank codes</a>
                                        </div>

                                        <div id="bank_code_validation" class="invalid-feedback" style="display: none;">
                                            Invalid bank code. Please enter a valid 5-digit bank-branch code.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="bank_name" class="form-label">Bank Name <small class="text-muted">(optional)</small></label>
                                        <input type="text" class="form-control" id="bank_name" name="bank_name"
                                               value="<?php echo htmlspecialchars($employee['bank_name'] ?? ''); ?>" readonly>
                                        <small class="form-text text-muted">Auto-populated when bank is selected</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="account_number" class="form-label">Account Number <small class="text-muted">(optional)</small></label>
                                        <input type="text" class="form-control" id="bank_account" name="bank_account"
                                               value="<?php echo htmlspecialchars($employee['bank_account'] ?? $employee['account_number'] ?? ''); ?>"
                                               placeholder="Enter account number">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bank_branch" class="form-label">Bank Branch <small class="text-muted">(auto-filled)</small></label>
                                        <input type="text" class="form-control" id="bank_branch" name="bank_branch"
                                               value="<?php echo htmlspecialchars($employee['bank_branch'] ?? ''); ?>"
                                               placeholder="Branch name will be auto-filled" readonly>
                                        <div class="form-text">Branch name will be populated when you enter a valid bank code</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info mb-0">
                                        <small>
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Banking Information:</strong> Select the bank code to auto-fill the bank name. This information will be used for salary payments and official records.
                                            Ensure all details are accurate and match your bank account.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <a href="index.php?page=employees" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> <?php echo $action === 'add' ? 'Add Employee' : 'Update Employee'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php elseif ($action === 'view'): ?>
        <!-- View Employee Details -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-eye"></i> Employee Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Employee Number:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo htmlspecialchars($employee['employee_number']); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Full Name:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo htmlspecialchars($employee['first_name'] . ' ' . ($employee['middle_name'] ? $employee['middle_name'] . ' ' : '') . $employee['last_name']); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>ID Number:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo htmlspecialchars($employee['id_number'] ?? 'Not provided'); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Email:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo htmlspecialchars($employee['email'] ?? 'Not provided'); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Phone:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo htmlspecialchars($employee['phone'] ?? 'Not provided'); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Hire Date:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo $employee['hire_date'] ? date('F j, Y', strtotime($employee['hire_date'])) : 'Not provided'; ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Department:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo htmlspecialchars($employee['department_name'] ?? 'Not assigned'); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Position:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo htmlspecialchars($employee['position_title'] ?? 'Not assigned'); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Employment Type:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo ucfirst($employee['contract_type'] ?? 'Not specified'); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Basic Salary:</strong>
                            </div>
                            <div class="col-md-8">
                                <?php echo formatCurrency($employee['basic_salary']); ?>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>Employment Status:</strong>
                            </div>
                            <div class="col-md-8">
                                <span class="badge status-<?php echo $employee['employment_status']; ?>">
                                    <?php echo ucfirst($employee['employment_status']); ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-university"></i> Banking Information</h6>
                            </div>
                            <div class="card-body">
                                <p><strong>Bank:</strong><br>
                                <?php echo htmlspecialchars($employee['bank_name'] ?? 'Not provided'); ?></p>

                                <p><strong>Branch:</strong><br>
                                <?php echo htmlspecialchars($employee['bank_branch'] ?? 'Not provided'); ?></p>

                                <p><strong>Account Number:</strong><br>
                                <?php echo htmlspecialchars($employee['account_number'] ?? 'Not provided'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end mt-4">
                    <a href="index.php?page=employees" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                    <a href="index.php?page=employees&action=edit&id=<?php echo $employee['id']; ?>" class="btn btn-primary me-2">
                        <i class="fas fa-edit"></i> Edit Employee
                    </a>
                    <a href="index.php?page=payslips&employee_id=<?php echo $employee['id']; ?>" class="btn btn-success">
                        <i class="fas fa-file-invoice-dollar"></i> View Payslips
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Bulk Import Modal -->
<div class="modal fade" id="bulkImportModal" tabindex="-1" aria-labelledby="bulkImportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, var(--kenya-green), var(--kenya-dark-green)); color: white;">
                <h5 class="modal-title" id="bulkImportModalLabel">
                    <i class="fas fa-upload"></i> Bulk Import Employees (CSV/Excel)
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Import Instructions</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📋 Required Fields:</h6>
                            <ul class="small">
                                <li><strong>first_name</strong> - Employee's first name (required)</li>
                                <li><strong>last_name</strong> - Employee's last name (required)</li>
                                <li><strong>basic_salary</strong> - Monthly salary amount (required)</li>
                                <li><strong>id_number</strong> - National ID number (optional, must be unique if provided)</li>
                                <li><strong>hire_date</strong> - Date format: YYYY-MM-DD (optional)</li>
                                <li><strong>employment_type</strong> - permanent, contract, casual, intern (optional, defaults to permanent)</li>
                                <li><strong>email</strong> - Employee email address (optional)</li>
                                <li><strong>phone</strong> - Phone number in +254XXXXXXXXX format (optional)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>📝 Important Notes:</h6>
                            <ul class="small">
                                <li>Department and position names must match existing records</li>
                                <li>Employment types: permanent, contract, casual, intern</li>
                                <li>Phone format: +254XXXXXXXXX (Kenyan format)</li>
                                <li>KRA PIN format: P********9A</li>
                                <li>Duplicate ID numbers will be skipped</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <form method="POST" action="index.php?page=employees&action=bulk_import" enctype="multipart/form-data" id="bulkImportForm">
                    <div class="mb-3">
                        <label for="csv_file" class="form-label">Select CSV or Excel File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv,.xlsx,.xls" required>
                        <div class="form-text">Maximum file size: 5MB. Supported formats: CSV (.csv), Excel (.xlsx, .xls)</div>
                    </div>

                    <div class="mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">CSV Template Structure</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead>
                                            <tr>
                                                <th>first_name*</th>
                                                <th>middle_name</th>
                                                <th>last_name*</th>
                                                <th>id_number*</th>
                                                <th>email</th>
                                                <th>phone</th>
                                                <th>hire_date*</th>
                                                <th>basic_salary*</th>
                                                <th>department_name</th>
                                                <th>position_title</th>
                                                <th>contract_type</th>
                                                <th>kra_pin</th>
                                                <th>nssf_number</th>
                                                <th>shif_id</th>
                                                <th>bank_code</th>
                                                <th>bank_name</th>
                                                <th>bank_branch</th>
                                                <th>account_number</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="text-muted">
                                                <td>John</td>
                                                <td>Doe</td>
                                                <td>Smith</td>
                                                <td>********</td>
                                                <td><EMAIL></td>
                                                <td>+************</td>
                                                <td>2024-01-15</td>
                                                <td>50000</td>
                                                <td>IT</td>
                                                <td>Developer</td>
                                                <td>permanent</td>
                                                <td>A********9B</td>
                                                <td>123456</td>
                                                <td>654321</td>
                                                <td>11</td>
                                                <td>Equity Bank</td>
                                                <td>Nairobi Branch</td>
                                                <td>**********</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <small class="text-muted">* Required fields</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-info" onclick="downloadTemplate()">
                            <i class="fas fa-download"></i> Download Template
                        </button>
                        <div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-upload"></i> Import Employees
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Kenya Bankers Association (KBA) Official Bank and Branch Codes
// Structure: 'bankCode-branchCode': {bankName, branchName, fullCode}
const kenyanBankCodes = {
    // Kenya Commercial Bank Limited (01)
    '01-340': {bankCode: '01', branchCode: '340', bankName: 'Kenya Commercial Bank Limited', branchName: 'Karen Waterfront Platinum', fullCode: '01340'},
    '01-341': {bankCode: '01', branchCode: '341', bankName: 'Kenya Commercial Bank Limited', branchName: 'KCB Ngong Branch', fullCode: '01341'},
    '01-325': {bankCode: '01', branchCode: '325', bankName: 'Kenya Commercial Bank Limited', branchName: 'Garden City', fullCode: '01325'},
    '01-326': {bankCode: '01', branchCode: '326', bankName: 'Kenya Commercial Bank Limited', branchName: 'JKIA', fullCode: '01326'},
    '01-327': {bankCode: '01', branchCode: '327', bankName: 'Kenya Commercial Bank Limited', branchName: 'Makutano', fullCode: '01327'},
    '01-330': {bankCode: '01', branchCode: '330', bankName: 'Kenya Commercial Bank Limited', branchName: 'KCB NextGen', fullCode: '01330'},
    '01-094': {bankCode: '01', branchCode: '094', bankName: 'Kenya Commercial Bank Limited', branchName: 'Head Office', fullCode: '01094'},
    '01-100': {bankCode: '01', branchCode: '100', bankName: 'Kenya Commercial Bank Limited', branchName: 'Moi Avenue Nairobi', fullCode: '01100'},
    '01-105': {bankCode: '01', branchCode: '105', bankName: 'Kenya Commercial Bank Limited', branchName: 'Kisumu', fullCode: '01105'},
    '01-103': {bankCode: '01', branchCode: '103', bankName: 'Kenya Commercial Bank Limited', branchName: 'Nakuru', fullCode: '01103'},

    // Standard Chartered Bank Kenya Ltd (02)
    '02-000': {bankCode: '02', branchCode: '000', bankName: 'Standard Chartered Bank Kenya Ltd', branchName: 'Eldoret', fullCode: '02000'},
    '02-008': {bankCode: '02', branchCode: '008', bankName: 'Standard Chartered Bank Kenya Ltd', branchName: 'Moi Avenue', fullCode: '02008'},
    '02-015': {bankCode: '02', branchCode: '015', bankName: 'Standard Chartered Bank Kenya Ltd', branchName: 'Westlands', fullCode: '02015'},
    '02-019': {bankCode: '02', branchCode: '019', bankName: 'Standard Chartered Bank Kenya Ltd', branchName: 'Harambee Avenue', fullCode: '02019'},

    // Absa Bank Kenya Plc (03)
    '03-001': {bankCode: '03', branchCode: '001', bankName: 'Absa Bank Kenya Plc', branchName: 'Head Office - Vpc', fullCode: '03001'},
    '03-075': {bankCode: '03', branchCode: '075', bankName: 'Absa Bank Kenya Plc', branchName: 'Moi Avenue-nairobi Branch', fullCode: '03075'},
    '03-073': {bankCode: '03', branchCode: '073', bankName: 'Absa Bank Kenya Plc', branchName: 'Westlands Branch', fullCode: '03073'},

    // NCBA Bank Kenya Plc (07)
    '07-101': {bankCode: '07', branchCode: '101', bankName: 'NCBA Bank Kenya Plc', branchName: 'City Centre', fullCode: '07101'},
    '07-102': {bankCode: '07', branchCode: '102', bankName: 'NCBA Bank Kenya Plc', branchName: 'NIC House', fullCode: '07102'},
    '07-105': {bankCode: '07', branchCode: '105', bankName: 'NCBA Bank Kenya Plc', branchName: 'Westlands', fullCode: '07105'},

    // Co-operative Bank of Kenya Limited (11)
    '11-000': {bankCode: '11', branchCode: '000', bankName: 'Co-operative Bank of Kenya Limited', branchName: 'Head Office', fullCode: '11000'},
    '11-002': {bankCode: '11', branchCode: '002', bankName: 'Co-operative Bank of Kenya Limited', branchName: 'Co-op House', fullCode: '11002'},
    '11-003': {bankCode: '11', branchCode: '003', bankName: 'Co-operative Bank of Kenya Limited', branchName: 'Kisumu', fullCode: '11003'},
    '11-006': {bankCode: '11', branchCode: '006', bankName: 'Co-operative Bank of Kenya Limited', branchName: 'Nakuru', fullCode: '11006'},

    // National Bank Of Kenya (12)
    '12-000': {bankCode: '12', branchCode: '000', bankName: 'National Bank Of Kenya', branchName: 'Central Business Unit', fullCode: '12000'},
    '12-002': {bankCode: '12', branchCode: '002', bankName: 'National Bank Of Kenya', branchName: 'Kenyatta', fullCode: '12002'},
    '12-035': {bankCode: '12', branchCode: '035', bankName: 'National Bank Of Kenya', branchName: 'Westlands', fullCode: '12035'},

    // Equity Bank Kenya (17)
    '17-001': {bankCode: '17', branchCode: '001', bankName: 'Equity Bank Kenya Limited', branchName: 'Head Office', fullCode: '17001'},
    '17-002': {bankCode: '17', branchCode: '002', bankName: 'Equity Bank Kenya Limited', branchName: 'Kenyatta Avenue', fullCode: '17002'},
    '17-003': {bankCode: '17', branchCode: '003', bankName: 'Equity Bank Kenya Limited', branchName: 'Mombasa', fullCode: '17003'},

    // Add more banks as needed...
};
    '02': {
        name: 'Standard Chartered Bank Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch', 'Eldoret Branch']
    },
    '03': {
        name: 'Absa Bank Kenya (formerly Barclays)',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch', 'Eldoret Branch', 'Thika Branch']
    },
    '07': {
        name: 'Commercial Bank of Africa (CBA)',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '11': {
        name: 'Cooperative Bank of Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch', 'Eldoret Branch', 'Thika Branch', 'Machakos Branch', 'Nyeri Branch', 'Meru Branch', 'Garissa Branch']
    },

    // Tier 2 Banks (Medium Commercial Banks)
    '12': {
        name: 'National Bank of Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch', 'Eldoret Branch']
    },
    '17': {
        name: 'Equity Bank Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch', 'Eldoret Branch', 'Thika Branch', 'Machakos Branch', 'Nyeri Branch', 'Meru Branch', 'Garissa Branch', 'Malindi Branch']
    },
    '18': {
        name: 'Housing Finance Company of Kenya (HFCK)',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch']
    },
    '19': {
        name: 'African Banking Corporation (ABC Bank)',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '23': {
        name: 'Consolidated Bank of Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch']
    },
    '25': {
        name: 'Credit Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '31': {
        name: 'Chase Bank Kenya (In Receivership)',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '32': {
        name: 'Imperial Bank Kenya (In Receivership)',
        branches: ['Head Office', 'Nairobi Branch']
    },
    '33': {
        name: 'Giro Commercial Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '35': {
        name: 'Bank of Africa Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '36': {
        name: 'Prime Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '37': {
        name: 'Transnational Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '39': {
        name: 'Stanbic Bank Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch']
    },
    '40': {
        name: 'NCBA Bank Kenya (formerly NIC Bank)',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch', 'Eldoret Branch']
    },
    '41': {
        name: 'Diamond Trust Bank Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '43': {
        name: 'Bank of Baroda Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '49': {
        name: 'Ecobank Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '50': {
        name: 'Paramount Universal Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '51': {
        name: 'Jamii Bora Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '53': {
        name: 'Victoria Commercial Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '54': {
        name: 'Guardian Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '55': {
        name: 'I&M Bank Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Westlands Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch']
    },
    '57': {
        name: 'Development Bank of Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '58': {
        name: 'Sidian Bank (formerly K-Rep Bank)',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch']
    },
    '59': {
        name: 'Spire Bank (formerly Equatorial Commercial Bank)',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '60': {
        name: 'UBA Kenya Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '61': {
        name: 'Mayfair Bank',
        branches: ['Head Office', 'Nairobi Branch']
    },
    '62': {
        name: 'Access Bank Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '63': {
        name: 'Citibank Kenya',
        branches: ['Head Office', 'Nairobi Branch']
    },
    '64': {
        name: 'Habib Bank AG Zurich',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '65': {
        name: 'Middle East Bank Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '66': {
        name: 'Bank of India Kenya',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch']
    },
    '68': {
        name: 'Gulf African Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch']
    },
    '70': {
        name: 'Family Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch', 'Nakuru Branch', 'Eldoret Branch']
    },
    '74': {
        name: 'First Community Bank',
        branches: ['Head Office', 'Nairobi Branch', 'Mombasa Branch', 'Kisumu Branch']
    }
};

    // Additional banks with numeric codes
    '02053': 'Standard Chartered Bank',
    '04012': 'Bank of Baroda',
    '05013': 'Bank of India',
    '06014': 'Bank of Africa Kenya',
    '07015': 'Prime Bank',
    '08058': 'Imperial Bank',
    '09169': 'Citibank',
    '10081': 'Habib Bank AG Zurich',
    '14017': 'Diamond Trust Bank',
    '15004': 'Consolidated Bank of Kenya',
    '16006': 'Credit Bank',
    '17053': 'African Banking Corporation',
    '18058': 'Trans National Bank',
    '19169': 'CFC Stanbic Bank',
    '20081': 'I&M Bank',
    '21017': 'Fidelity Commercial Bank',
    '22004': 'Dubai Bank Kenya',
    '23006': 'Guaranty Trust Bank',
    '24053': 'Family Bank',
    '25058': 'Giro Commercial Bank',
    '26169': 'Guardian Bank',
    '28081': 'Victoria Commercial Bank',
    '29017': 'Chase Bank Kenya',
    '30004': 'Middle East Bank Kenya',
    '31006': 'Paramount Universal Bank',
    '32053': 'Jamii Bora Bank',
    '33058': 'Development Bank of Kenya',
    '34169': 'Housing Finance Company of Kenya',
    '35081': 'NIC Bank',
    '36017': 'Commercial Bank of Africa',
    '37004': 'Sidian Bank',
    '38006': 'UBA Kenya Bank',
    '39053': 'Ecobank Kenya',
    '40058': 'Spire Bank',
    '41169': 'Mayfair Bank',
    '42081': 'Access Bank Kenya',
    '43017': 'Kingdom Bank',
    '44004': 'DIB Bank Kenya',
    '45006': 'NCBA Bank Kenya',
    '47053': 'HFC Limited',
    '48058': 'SBM Bank Kenya',
    '16019': 'Stanbic Bank',
    '49020': 'First Community Bank',
    '50021': 'Oriental Commercial Bank',
    '51022': 'Equatorial Commercial Bank'
};

// Load bank codes from the comprehensive list
let allBankCodes = {};

// Load bank codes from server
async function loadBankCodes() {
    try {
        const response = await fetch('get_bank_codes.php');
        const data = await response.json();
        allBankCodes = data;

        // Populate datalist
        const datalist = document.getElementById('bank_codes_list');
        if (datalist) {
            datalist.innerHTML = '';
            Object.keys(allBankCodes).forEach(code => {
                const option = document.createElement('option');
                option.value = code;
                option.textContent = `${code} - ${allBankCodes[code].bankName} - ${allBankCodes[code].branchName}`;
                datalist.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load bank codes:', error);
        // Fallback to basic codes if loading fails
        loadFallbackBankCodes();
    }
}

// Fallback bank codes if file loading fails
function loadFallbackBankCodes() {
    allBankCodes = {
        '01094': {bankName: 'Kenya Commercial Bank Limited', branchName: 'Head Office', bankCode: '01', branchCode: '094'},
        '01100': {bankName: 'Kenya Commercial Bank Limited', branchName: 'Moi Avenue Nairobi', bankCode: '01', branchCode: '100'},
        '02008': {bankName: 'Standard Chartered Bank Kenya Ltd', branchName: 'Moi Avenue', bankCode: '02', branchCode: '008'},
        '03001': {bankName: 'Absa Bank Kenya Plc', branchName: 'Head Office - Vpc', bankCode: '03', branchCode: '001'},
        '11000': {bankName: 'Co-operative Bank of Kenya Limited', branchName: 'Head Office', bankCode: '11', branchCode: '000'},
        '12000': {bankName: 'National Bank Of Kenya', branchName: 'Central Business Unit', bankCode: '12', branchCode: '000'}
    };
}

// Validate bank code input
function validateBankCode(input) {
    const code = input.value.trim();
    const validationDiv = document.getElementById('bank_code_validation');

    // Remove any non-numeric characters and limit to 5 digits
    let cleanCode = code.replace(/[^0-9]/g, '').substring(0, 5);

    // Ensure leading zeros are preserved by padding to 5 digits if numeric
    if (cleanCode.length > 0 && cleanCode.length <= 5) {
        cleanCode = cleanCode.padStart(5, '0');
    }

    // Update input value with properly formatted code
    if (cleanCode !== code && cleanCode.length === 5) {
        input.value = cleanCode;
    } else if (code.replace(/[^0-9]/g, '') !== code) {
        input.value = code.replace(/[^0-9]/g, '').substring(0, 5);
    }

    // Validate if code exists
    if (input.value.length === 5) {
        const finalCode = input.value;
        if (allBankCodes[finalCode]) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
            validationDiv.style.display = 'none';
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            validationDiv.style.display = 'block';
        }
    } else if (input.value.length > 0) {
        input.classList.remove('is-valid', 'is-invalid');
        validationDiv.style.display = 'none';
    } else {
        input.classList.remove('is-valid', 'is-invalid');
        validationDiv.style.display = 'none';
    }
}

// Update bank name and branch when bank code is entered
function updateBankDetails() {
    const bankCodeInput = document.getElementById('bank_code');
    const bankNameInput = document.getElementById('bank_name');
    const bankBranchInput = document.getElementById('bank_branch');

    if (bankCodeInput && bankNameInput && bankBranchInput) {
        let code = bankCodeInput.value.trim();

        // Ensure code is properly formatted as 5-digit string with leading zeros
        if (code && /^\d{1,5}$/.test(code)) {
            code = code.padStart(5, '0');
            bankCodeInput.value = code; // Update input with properly formatted code
        }

        if (code && allBankCodes[code]) {
            const bankData = allBankCodes[code];
            bankNameInput.value = bankData.bankName;
            bankBranchInput.value = bankData.branchName;
        } else {
            bankNameInput.value = '';
            bankBranchInput.value = '';
        }
    }
}

// Show bank codes modal
function showBankCodesModal() {
    // Create modal if it doesn't exist
    let modal = document.getElementById('bankCodesModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'bankCodesModal';
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">🏦 Kenya Bank Codes Reference</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="bankCodesSearch"
                                   placeholder="Search bank or branch name..."
                                   oninput="filterBankCodes()">
                        </div>
                        <div id="bankCodesTable" style="max-height: 400px; overflow-y: auto;">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th>Code</th>
                                        <th>Bank Name</th>
                                        <th>Branch Name</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="bankCodesTableBody">
                                    <tr><td colspan="4">Loading bank codes...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);

        // Load bank codes into modal
        loadBankCodesIntoModal();
    }

    // Show modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// Load bank codes into modal table
function loadBankCodesIntoModal() {
    const tbody = document.getElementById('bankCodesTableBody');
    if (!tbody) return;

    if (Object.keys(allBankCodes).length === 0) {
        tbody.innerHTML = '<tr><td colspan="4">No bank codes loaded</td></tr>';
        return;
    }

    let html = '';
    Object.keys(allBankCodes).forEach(code => {
        const bank = allBankCodes[code];
        html += `
            <tr>
                <td><strong>${code}</strong></td>
                <td>${bank.bankName}</td>
                <td>${bank.branchName}</td>
                <td><button class="btn btn-sm btn-primary" onclick="selectBankCode('${code}')">Select</button></td>
            </tr>
        `;
    });

    tbody.innerHTML = html;
}

// Filter bank codes in modal
function filterBankCodes() {
    const searchTerm = document.getElementById('bankCodesSearch').value.toLowerCase();
    const rows = document.querySelectorAll('#bankCodesTableBody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

// Select bank code from modal
function selectBankCode(code) {
    const bankCodeInput = document.getElementById('bank_code');
    if (bankCodeInput) {
        bankCodeInput.value = code;
        validateBankCode(bankCodeInput);
        updateBankDetails();
    }

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('bankCodesModal'));
    if (modal) modal.hide();
}

// Initialize bank details on page load
document.addEventListener('DOMContentLoaded', function() {
    loadBankCodes().then(() => {
        updateBankDetails();
    });
    initializeSearchableSelect();
});

// Download template with format selection
function downloadTemplate() {
    // Create a simple modal or dropdown for format selection
    const format = prompt('Select format:\n1. CSV (.csv)\n2. Excel (.xlsx)\n\nEnter 1 or 2:', '1');

    if (format === '1' || format === null || format === '') {
        window.open('download_template.php?type=employees&format=csv', '_blank');
    } else if (format === '2') {
        window.open('download_template.php?type=employees&format=excel', '_blank');
    } else {
        alert('Invalid selection. Please try again.');
    }
}

// Searchable Select Implementation
function initializeSearchableSelect() {
    const selectElement = document.getElementById('bank_code');
    if (!selectElement || !selectElement.classList.contains('searchable-select')) return;

    // Create container
    const container = document.createElement('div');
    container.className = 'searchable-select-container';
    selectElement.parentNode.insertBefore(container, selectElement);

    // Create input
    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'form-control searchable-select-input';
    input.placeholder = selectElement.dataset.placeholder || 'Search...';
    input.autocomplete = 'off';

    // Create dropdown
    const dropdown = document.createElement('div');
    dropdown.className = 'searchable-select-dropdown';

    // Store original options
    const options = Array.from(selectElement.options).map(option => ({
        value: option.value,
        text: option.textContent,
        selected: option.selected
    }));

    // Set initial value if there's a selected option
    const selectedOption = options.find(opt => opt.selected);
    if (selectedOption) {
        input.value = selectedOption.text;
    }

    // Hide original select
    selectElement.style.display = 'none';

    // Add elements to container
    container.appendChild(input);
    container.appendChild(dropdown);
    container.appendChild(selectElement);

    // Filter and display options
    function filterOptions(searchTerm = '') {
        dropdown.innerHTML = '';
        const filteredOptions = options.filter(option => {
            if (!option.value) return false; // Skip empty option
            return option.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
                   option.value.includes(searchTerm);
        });

        filteredOptions.forEach((option, index) => {
            const optionElement = document.createElement('div');
            optionElement.className = 'searchable-select-option';
            optionElement.textContent = option.text;
            optionElement.dataset.value = option.value;

            if (option.selected) {
                optionElement.classList.add('selected');
            }

            optionElement.addEventListener('click', function() {
                selectOption(option);
            });

            dropdown.appendChild(optionElement);
        });

        return filteredOptions;
    }

    // Select an option
    function selectOption(option) {
        input.value = option.text;
        selectElement.value = option.value;

        // Update selected state
        options.forEach(opt => opt.selected = false);
        option.selected = true;

        // Update visual state
        dropdown.querySelectorAll('.searchable-select-option').forEach(el => {
            el.classList.remove('selected');
        });
        dropdown.querySelector(`[data-value="${option.value}"]`)?.classList.add('selected');

        hideDropdown();
        updateBankName(); // Trigger bank name update

        // Trigger change event
        selectElement.dispatchEvent(new Event('change'));
    }

    // Show dropdown
    function showDropdown() {
        filterOptions(input.value);
        dropdown.style.display = 'block';
    }

    // Hide dropdown
    function hideDropdown() {
        dropdown.style.display = 'none';
    }

    // Event listeners
    input.addEventListener('focus', showDropdown);
    input.addEventListener('input', function() {
        filterOptions(this.value);
        showDropdown();
    });

    // Keyboard navigation
    input.addEventListener('keydown', function(e) {
        const visibleOptions = dropdown.querySelectorAll('.searchable-select-option');
        const highlighted = dropdown.querySelector('.highlighted');
        let currentIndex = highlighted ? Array.from(visibleOptions).indexOf(highlighted) : -1;

        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                if (currentIndex < visibleOptions.length - 1) {
                    if (highlighted) highlighted.classList.remove('highlighted');
                    visibleOptions[currentIndex + 1].classList.add('highlighted');
                }
                break;

            case 'ArrowUp':
                e.preventDefault();
                if (currentIndex > 0) {
                    if (highlighted) highlighted.classList.remove('highlighted');
                    visibleOptions[currentIndex - 1].classList.add('highlighted');
                }
                break;

            case 'Enter':
                e.preventDefault();
                if (highlighted) {
                    const option = options.find(opt => opt.value === highlighted.dataset.value);
                    if (option) selectOption(option);
                }
                break;

            case 'Escape':
                hideDropdown();
                break;
        }
    });

    // Click outside to close
    document.addEventListener('click', function(e) {
        if (!container.contains(e.target)) {
            hideDropdown();
        }
    });

    // Initial filter
    filterOptions();
}

// File validation
document.getElementById('csv_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Check file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB');
            this.value = '';
            return;
        }

        // Check file type
        const fileName = file.name.toLowerCase();
        const allowedExtensions = ['.csv', '.xlsx', '.xls'];
        const isValidFile = allowedExtensions.some(ext => fileName.endsWith(ext));

        if (!isValidFile) {
            alert('Please select a CSV (.csv) or Excel (.xlsx, .xls) file');
            this.value = '';
            return;
        }
    }
});

// Form submission with loading state
document.getElementById('bulkImportForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Importing...';

    // Re-enable button after 30 seconds as fallback
    setTimeout(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }, 30000);
});

// Search functionality for employee list
<?php if ($action === 'list'): ?>
document.querySelector('.search-input').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});
<?php endif; ?>

// Currency formatting
document.querySelectorAll('.currency-input').forEach(input => {
    input.addEventListener('blur', function() {
        const value = parseFloat(this.value);
        if (!isNaN(value)) {
            this.value = value.toFixed(2);
        }
    });
});

// Account number validation
document.getElementById('bank_account')?.addEventListener('input', function() {
    // Remove non-numeric characters
    this.value = this.value.replace(/[^0-9]/g, '');

    // Limit to 20 characters (typical max for Kenyan banks)
    if (this.value.length > 20) {
        this.value = this.value.substring(0, 20);
    }
});

// Bank code validation and formatting
document.getElementById('bank_code')?.addEventListener('change', function() {
    const accountField = document.getElementById('bank_account');
    const selectedBank = this.options[this.selectedIndex].text;

    // Clear account number when bank changes
    if (accountField && accountField.value) {
        if (confirm('Changing the bank will clear the account number. Continue?')) {
            accountField.value = '';
        } else {
            // Revert to previous selection
            this.selectedIndex = 0;
            updateBankDetails();
        }
    }
});
</script>

<style>
:root {
    --kenya-green: #006b3f;
    --kenya-dark-green: #004d2e;
    --kenya-red: #ce1126;
    --kenya-black: #000000;
    --kenya-white: #ffffff;
}

.btn-group .btn {
    margin-right: 0;
}

.table th {
    background-color: var(--kenya-green);
    color: white;
    border-color: var(--kenya-dark-green);
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0, 107, 63, 0.05);
}

.modal-header {
    border-bottom: 3px solid var(--kenya-red);
}

.alert-info {
    border-left: 4px solid var(--kenya-green);
}

.card-header {
    background-color: rgba(0, 107, 63, 0.1);
    border-bottom: 2px solid var(--kenya-green);
}

.btn-success {
    background: linear-gradient(135deg, var(--kenya-green), var(--kenya-dark-green));
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--kenya-dark-green), var(--kenya-green));
    transform: translateY(-1px);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border: none;
}

.search-input {
    border: 2px solid var(--kenya-green);
}

.search-input:focus {
    border-color: var(--kenya-dark-green);
    box-shadow: 0 0 0 0.2rem rgba(0, 107, 63, 0.25);
}

/* Searchable Select Styles */
.searchable-select-container {
    position: relative;
}

.searchable-select-input {
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    background-color: #fff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    cursor: pointer;
}

.searchable-select-input:focus {
    border-color: var(--kenya-green);
    box-shadow: 0 0 0 0.2rem rgba(0, 107, 63, 0.25);
    outline: 0;
}

.searchable-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.searchable-select-option {
    padding: 0.5rem 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

.searchable-select-option:hover {
    background-color: #f8f9fa;
}

.searchable-select-option.selected {
    background-color: var(--kenya-green);
    color: white;
}

.searchable-select-option.highlighted {
    background-color: #e9ecef;
}

/* Delete functionality styles */
.employee-checkbox:checked {
    background-color: var(--kenya-red);
    border-color: var(--kenya-red);
}

#bulkDeleteControls {
    border-left: 4px solid var(--kenya-red);
}
</style>

<script>
// Delete functionality
function deleteEmployee(employeeId, employeeName) {
    if (confirm(`Are you sure you want to delete employee "${employeeName}"?\n\nNote: If the employee has payroll records, they will be deactivated instead of deleted.`)) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'index.php?page=employees&action=delete';

        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'employee_id';
        input.value = employeeId;

        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.employee-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBulkDeleteButton();
}

function updateBulkDeleteButton() {
    const checkboxes = document.querySelectorAll('.employee-checkbox:checked');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const bulkDeleteControls = document.getElementById('bulkDeleteControls');
    const selectedCount = document.getElementById('selectedCount');

    if (checkboxes.length > 0) {
        bulkDeleteBtn.style.display = 'inline-block';
        bulkDeleteControls.style.display = 'block';
        selectedCount.textContent = `${checkboxes.length} employee${checkboxes.length > 1 ? 's' : ''} selected`;
    } else {
        bulkDeleteBtn.style.display = 'none';
        bulkDeleteControls.style.display = 'none';
    }

    // Update select all checkbox
    const allCheckboxes = document.querySelectorAll('.employee-checkbox');
    const selectAll = document.getElementById('selectAll');
    selectAll.checked = allCheckboxes.length > 0 && checkboxes.length === allCheckboxes.length;
}

function toggleBulkDelete() {
    const bulkDeleteControls = document.getElementById('bulkDeleteControls');
    bulkDeleteControls.style.display = bulkDeleteControls.style.display === 'none' ? 'block' : 'none';
}

function confirmBulkDelete() {
    const checkboxes = document.querySelectorAll('.employee-checkbox:checked');
    if (checkboxes.length === 0) {
        alert('Please select employees to delete.');
        return;
    }

    if (confirm(`Are you sure you want to delete ${checkboxes.length} employee${checkboxes.length > 1 ? 's' : ''}?\n\nNote: Employees with payroll records will be deactivated instead of deleted.`)) {
        document.getElementById('bulkDeleteForm').submit();
    }
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.employee-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    document.getElementById('selectAll').checked = false;
    updateBulkDeleteButton();
}
</script>
