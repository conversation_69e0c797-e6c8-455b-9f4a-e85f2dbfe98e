-- =====================================================
-- KENYAN PAYROLL MANAGEMENT SYSTEM - COMPLETE DATABASE SCHEMA
-- =====================================================
-- Updated: July 2025
-- Includes: SHIF ID compliance, Enhanced banking system, Complete table structure
-- Database: MySQL/MariaDB (for SQLite, change AUTO_INCREMENT to AUTOINCREMENT and ENGINE clauses)

-- Drop existing tables (in correct order to handle foreign keys)
DROP TABLE IF EXISTS payroll_records;
DROP TABLE IF EXISTS allowances;
DROP TABLE IF EXISTS deductions;
DROP TABLE IF EXISTS leave_applications;
DROP TABLE IF EXISTS employees;
DROP TABLE IF EXISTS job_positions;
DROP TABLE IF EXISTS departments;
DROP TABLE IF EXISTS payroll_periods;
DROP TABLE IF EXISTS companies;
DROP TABLE IF EXISTS users;

-- =====================================================
-- 1. COMPANIES TABLE
-- =====================================================
CREATE TABLE companies (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    tax_id VARCHAR(50),
    registration_number VARCHAR(50),
    logo_path VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 2. USERS TABLE
-- =====================================================
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    company_id INT NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role ENUM('admin', 'hr', 'employee') DEFAULT 'employee',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);

-- =====================================================
-- 3. DEPARTMENTS TABLE
-- =====================================================
CREATE TABLE departments (
    id INTEGER PRIMARY KEY,
    company_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    manager_id INT,
    budget DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);

-- =====================================================
-- 4. JOB POSITIONS TABLE
-- =====================================================
CREATE TABLE job_positions (
    id INTEGER PRIMARY KEY,
    company_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    department_id INT,
    min_salary DECIMAL(15,2) DEFAULT 0,
    max_salary DECIMAL(15,2) DEFAULT 0,
    requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
);

-- =====================================================
-- 5. PAYROLL PERIODS TABLE
-- =====================================================
CREATE TABLE payroll_periods (
    id INTEGER PRIMARY KEY,
    company_id INT NOT NULL,
    period_name VARCHAR(50) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    pay_date DATE NOT NULL,
    status ENUM('draft', 'processing', 'completed', 'paid') DEFAULT 'draft',
    total_gross DECIMAL(15,2) DEFAULT 0,
    total_deductions DECIMAL(15,2) DEFAULT 0,
    total_net DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    UNIQUE KEY unique_period_company (company_id, period_name)
);

-- =====================================================
-- 6. EMPLOYEES TABLE (Enhanced with SHIF ID and Banking)
-- =====================================================
CREATE TABLE employees (
    id INTEGER PRIMARY KEY,
    company_id INT NOT NULL,
    employee_number VARCHAR(20) UNIQUE NOT NULL,
    
    -- Personal Information
    first_name VARCHAR(50) NOT NULL,
    middle_name VARCHAR(50),
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    id_number VARCHAR(20),
    date_of_birth DATE,
    gender ENUM('male', 'female', 'other'),
    address TEXT,
    
    -- Employment Information
    department_id INT,
    position_id INT,
    hire_date DATE,
    contract_type ENUM('permanent', 'contract', 'temporary', 'intern') DEFAULT 'permanent',
    employment_status ENUM('active', 'inactive', 'terminated', 'suspended') DEFAULT 'active',
    basic_salary DECIMAL(15,2) DEFAULT 0,
    
    -- Statutory Information (Kenya - Updated for SHIF)
    kra_pin VARCHAR(20),           -- Kenya Revenue Authority PIN
    nssf_number VARCHAR(20),       -- National Social Security Fund Number
    shif_id VARCHAR(20),           -- Social Health Insurance Fund ID (formerly NHIF)
    
    -- Enhanced Banking Information (Kenya Banking System)
    bank_code VARCHAR(10),         -- 5-digit bank-branch code (e.g., '01094')
    bank_name VARCHAR(100),        -- Full bank name
    bank_branch VARCHAR(100),      -- Branch name
    bank_account VARCHAR(50),      -- Account number (preserves leading zeros)
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (position_id) REFERENCES job_positions(id) ON DELETE SET NULL
);

-- =====================================================
-- 7. ALLOWANCES TABLE
-- =====================================================
CREATE TABLE allowances (
    id INTEGER PRIMARY KEY,
    company_id INT NOT NULL,
    employee_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    is_taxable BOOLEAN DEFAULT TRUE,
    is_recurring BOOLEAN DEFAULT TRUE,
    effective_date DATE,
    end_date DATE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE
);

-- =====================================================
-- 8. DEDUCTIONS TABLE
-- =====================================================
CREATE TABLE deductions (
    id INTEGER PRIMARY KEY,
    company_id INT NOT NULL,
    employee_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    is_recurring BOOLEAN DEFAULT TRUE,
    effective_date DATE,
    end_date DATE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE
);

-- =====================================================
-- 9. LEAVE APPLICATIONS TABLE
-- =====================================================
CREATE TABLE leave_applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    employee_id INT NOT NULL,
    leave_type ENUM('annual', 'sick', 'maternity', 'paternity', 'compassionate', 'study', 'other') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    days_requested INT NOT NULL,
    reason TEXT,
    status ENUM('pending', 'approved', 'rejected', 'cancelled') DEFAULT 'pending',
    approved_by INT,
    approved_at TIMESTAMP NULL,
    comments TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 10. PAYROLL RECORDS TABLE (Updated with SHIF)
-- =====================================================
CREATE TABLE payroll_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    employee_id INT NOT NULL,
    payroll_period_id INT NOT NULL,
    
    -- Salary Components
    basic_salary DECIMAL(15,2) DEFAULT 0,
    total_allowances DECIMAL(15,2) DEFAULT 0,
    overtime_hours DECIMAL(8,2) DEFAULT 0,
    overtime_amount DECIMAL(15,2) DEFAULT 0,
    gross_pay DECIMAL(15,2) DEFAULT 0,
    
    -- Tax and Statutory Deductions (Updated for SHIF)
    taxable_income DECIMAL(15,2) DEFAULT 0,
    paye_tax DECIMAL(15,2) DEFAULT 0,
    nssf_deduction DECIMAL(15,2) DEFAULT 0,
    shif_deduction DECIMAL(15,2) DEFAULT 0,    -- Updated from nhif_deduction
    housing_levy DECIMAL(15,2) DEFAULT 0,
    
    -- Other Deductions
    other_deductions DECIMAL(15,2) DEFAULT 0,
    total_deductions DECIMAL(15,2) DEFAULT 0,
    
    -- Final Amounts
    net_pay DECIMAL(15,2) DEFAULT 0,
    days_worked INT DEFAULT 30,
    
    -- Status and Timestamps
    status ENUM('draft', 'calculated', 'approved', 'paid') DEFAULT 'draft',
    calculated_at TIMESTAMP NULL,
    approved_at TIMESTAMP NULL,
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key Constraints
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (payroll_period_id) REFERENCES payroll_periods(id) ON DELETE CASCADE,
    
    -- Ensure one record per employee per period
    UNIQUE KEY unique_employee_period (employee_id, payroll_period_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Companies indexes
CREATE INDEX idx_companies_name ON companies(name);

-- Users indexes
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- Departments indexes
CREATE INDEX idx_departments_company_id ON departments(company_id);
CREATE INDEX idx_departments_manager_id ON departments(manager_id);

-- Job positions indexes
CREATE INDEX idx_job_positions_company_id ON job_positions(company_id);
CREATE INDEX idx_job_positions_department_id ON job_positions(department_id);

-- Payroll periods indexes
CREATE INDEX idx_payroll_periods_company_id ON payroll_periods(company_id);
CREATE INDEX idx_payroll_periods_status ON payroll_periods(status);
CREATE INDEX idx_payroll_periods_dates ON payroll_periods(start_date, end_date);

-- Employees indexes (Enhanced for banking and statutory fields)
CREATE INDEX idx_employees_company_id ON employees(company_id);
CREATE INDEX idx_employees_employee_number ON employees(employee_number);
CREATE INDEX idx_employees_department_id ON employees(department_id);
CREATE INDEX idx_employees_position_id ON employees(position_id);
CREATE INDEX idx_employees_email ON employees(email);
CREATE INDEX idx_employees_id_number ON employees(id_number);
CREATE INDEX idx_employees_kra_pin ON employees(kra_pin);
CREATE INDEX idx_employees_nssf_number ON employees(nssf_number);
CREATE INDEX idx_employees_shif_id ON employees(shif_id);
CREATE INDEX idx_employees_bank_code ON employees(bank_code);
CREATE INDEX idx_employees_employment_status ON employees(employment_status);
CREATE INDEX idx_employees_hire_date ON employees(hire_date);

-- Allowances indexes
CREATE INDEX idx_allowances_company_id ON allowances(company_id);
CREATE INDEX idx_allowances_employee_id ON allowances(employee_id);
CREATE INDEX idx_allowances_effective_date ON allowances(effective_date);

-- Deductions indexes
CREATE INDEX idx_deductions_company_id ON deductions(company_id);
CREATE INDEX idx_deductions_employee_id ON deductions(employee_id);
CREATE INDEX idx_deductions_effective_date ON deductions(effective_date);

-- Leave applications indexes
CREATE INDEX idx_leave_applications_company_id ON leave_applications(company_id);
CREATE INDEX idx_leave_applications_employee_id ON leave_applications(employee_id);
CREATE INDEX idx_leave_applications_status ON leave_applications(status);
CREATE INDEX idx_leave_applications_dates ON leave_applications(start_date, end_date);

-- Payroll records indexes
CREATE INDEX idx_payroll_records_company_id ON payroll_records(company_id);
CREATE INDEX idx_payroll_records_employee_id ON payroll_records(employee_id);
CREATE INDEX idx_payroll_records_payroll_period_id ON payroll_records(payroll_period_id);
CREATE INDEX idx_payroll_records_status ON payroll_records(status);

-- =====================================================
-- SAMPLE DATA INSERTION
-- =====================================================

-- Insert default company
INSERT INTO companies (name, address, phone, email, tax_id, registration_number) VALUES
('Sample Company Ltd', 'P.O. Box 12345, Nairobi, Kenya', '+254700123456', '<EMAIL>', 'P051234567A', 'CPR/2020/123456');

-- Insert default admin user (password: admin123 - change in production!)
INSERT INTO users (company_id, username, email, password_hash, first_name, last_name, role) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System', 'Administrator', 'admin');

-- Insert sample departments
INSERT INTO departments (company_id, name, description) VALUES
(1, 'Human Resources', 'Manages employee relations, recruitment, and HR policies'),
(1, 'Information Technology', 'Manages IT infrastructure, software development, and technical support'),
(1, 'Finance & Accounting', 'Handles financial planning, accounting, and budget management'),
(1, 'Operations', 'Manages day-to-day business operations and processes'),
(1, 'Sales & Marketing', 'Handles sales activities and marketing campaigns');

-- Insert sample job positions
INSERT INTO job_positions (company_id, title, description, department_id, min_salary, max_salary) VALUES
(1, 'HR Manager', 'Oversees HR operations and employee relations', 1, 80000, 120000),
(1, 'Software Developer', 'Develops and maintains software applications', 2, 60000, 100000),
(1, 'Accountant', 'Manages financial records and accounting processes', 3, 50000, 80000),
(1, 'Operations Manager', 'Oversees daily operations and process improvement', 4, 70000, 110000),
(1, 'Sales Executive', 'Manages client relationships and sales activities', 5, 40000, 70000);

-- Insert sample payroll period
INSERT INTO payroll_periods (company_id, period_name, start_date, end_date, pay_date, status) VALUES
(1, 'July 2025', '2025-07-01', '2025-07-31', '2025-08-05', 'draft');

-- =====================================================
-- SCHEMA DOCUMENTATION
-- =====================================================

/*
KENYAN PAYROLL MANAGEMENT SYSTEM - SCHEMA NOTES

1. SHIF COMPLIANCE:
   - Updated from NHIF to SHIF (Social Health Insurance Fund)
   - employees.shif_id: Stores SHIF ID numbers
   - payroll_records.shif_deduction: Stores SHIF deductions

2. ENHANCED BANKING SYSTEM:
   - employees.bank_code: 5-digit bank-branch codes (e.g., '01094')
   - employees.bank_name: Full bank name
   - employees.bank_branch: Branch name
   - employees.bank_account: Account number (preserves leading zeros)

3. STATUTORY FIELDS (KENYA):
   - kra_pin: Kenya Revenue Authority PIN
   - nssf_number: National Social Security Fund Number
   - shif_id: Social Health Insurance Fund ID

4. PAYROLL CALCULATIONS:
   - Basic salary + allowances + overtime = Gross pay
   - Gross pay - (PAYE + NSSF + SHIF + Housing Levy + Other) = Net pay
   - All amounts in KES (Kenyan Shillings)

5. PERFORMANCE:
   - Comprehensive indexes for fast queries
   - Foreign key constraints for data integrity
   - UTF-8 encoding for international characters

6. SECURITY:
   - Password hashing for user accounts
   - Role-based access control
   - Company-level data isolation

7. EXTENSIBILITY:
   - Flexible allowances and deductions system
   - Leave management integration
   - Multi-company support

For SQLite version:
- Change AUTO_INCREMENT to AUTOINCREMENT
- Remove ENGINE and CHARSET clauses
- Adjust data types as needed (INT → INTEGER, etc.)
*/
