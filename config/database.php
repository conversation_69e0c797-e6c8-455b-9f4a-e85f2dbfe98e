<?php
/**
 * Database Configuration - Auto-generated by robust installer
 * This file is created by the robust database installer
 */

require_once __DIR__ . '/DatabaseManager.php';

// Check if we have a saved configuration
$configFile = __DIR__ . '/database_config.php';

if (file_exists($configFile)) {
    // Use saved configuration
    $config = include $configFile;
    $database = new DatabaseManager($config);
} else {
    // Fallback to SQLite for development
    $database = new DatabaseManager([
        'type' => 'sqlite',
        'path' => __DIR__ . '/../database/kenyan_payroll.sqlite'
    ]);
}

// Get the database connection
$db = $database->getConnection();

// Legacy compatibility - some old code might expect this
class Database {
    private $dbManager;
    
    public function __construct() {
        global $database;
        $this->dbManager = $database;
    }
    
    public function getConnection() {
        return $this->dbManager->getConnection();
    }
}
?>
